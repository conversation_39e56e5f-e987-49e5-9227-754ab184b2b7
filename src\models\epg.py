"""
EPG (Electronic Program Guide) data models
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
import json


@dataclass
class EPGProgram:
    """Represents a single EPG program/show"""
    
    id: str
    channel_id: str
    title: str
    start_time: datetime
    end_time: datetime
    description: Optional[str] = None
    category: Optional[str] = None
    genre: Optional[str] = None
    rating: Optional[str] = None
    episode_number: Optional[int] = None
    season_number: Optional[int] = None
    year: Optional[int] = None
    director: Optional[str] = None
    cast: List[str] = field(default_factory=list)
    poster: Optional[str] = None
    is_live: bool = False
    is_premiere: bool = False
    is_repeat: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert program to dictionary"""
        return {
            'id': self.id,
            'channel_id': self.channel_id,
            'title': self.title,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat(),
            'description': self.description,
            'category': self.category,
            'genre': self.genre,
            'rating': self.rating,
            'episode_number': self.episode_number,
            'season_number': self.season_number,
            'year': self.year,
            'director': self.director,
            'cast': json.dumps(self.cast),
            'poster': self.poster,
            'is_live': self.is_live,
            'is_premiere': self.is_premiere,
            'is_repeat': self.is_repeat,
            'metadata': json.dumps(self.metadata)
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EPGProgram':
        """Create program from dictionary"""
        cast = data.get('cast', '[]')
        if isinstance(cast, str):
            cast = json.loads(cast)
        
        metadata = data.get('metadata', '{}')
        if isinstance(metadata, str):
            metadata = json.loads(metadata)
        
        return cls(
            id=data['id'],
            channel_id=data['channel_id'],
            title=data['title'],
            start_time=datetime.fromisoformat(data['start_time']),
            end_time=datetime.fromisoformat(data['end_time']),
            description=data.get('description'),
            category=data.get('category'),
            genre=data.get('genre'),
            rating=data.get('rating'),
            episode_number=data.get('episode_number'),
            season_number=data.get('season_number'),
            year=data.get('year'),
            director=data.get('director'),
            cast=cast,
            poster=data.get('poster'),
            is_live=data.get('is_live', False),
            is_premiere=data.get('is_premiere', False),
            is_repeat=data.get('is_repeat', False),
            metadata=metadata
        )
    
    @property
    def duration(self) -> timedelta:
        """Get program duration"""
        return self.end_time - self.start_time
    
    @property
    def duration_minutes(self) -> int:
        """Get duration in minutes"""
        return int(self.duration.total_seconds() / 60)
    
    def is_current(self) -> bool:
        """Check if program is currently airing"""
        now = datetime.now()
        return self.start_time <= now <= self.end_time
    
    def is_upcoming(self) -> bool:
        """Check if program is upcoming"""
        return self.start_time > datetime.now()
    
    def is_past(self) -> bool:
        """Check if program has ended"""
        return self.end_time < datetime.now()
    
    def get_progress_percentage(self) -> float:
        """Get current progress percentage if program is airing"""
        if not self.is_current():
            return 0.0
        
        now = datetime.now()
        total_duration = self.duration.total_seconds()
        elapsed = (now - self.start_time).total_seconds()
        
        return min(100.0, max(0.0, (elapsed / total_duration) * 100))
    
    def get_time_remaining(self) -> timedelta:
        """Get time remaining if program is current"""
        if not self.is_current():
            return timedelta(0)
        
        return self.end_time - datetime.now()
    
    def get_formatted_time_range(self) -> str:
        """Get formatted time range string"""
        start_str = self.start_time.strftime("%H:%M")
        end_str = self.end_time.strftime("%H:%M")
        return f"{start_str} - {end_str}"


@dataclass
class EPGChannel:
    """Represents EPG data for a channel"""
    
    channel_id: str
    programs: List[EPGProgram] = field(default_factory=list)
    last_updated: datetime = field(default_factory=datetime.now)
    
    def add_program(self, program: EPGProgram) -> None:
        """Add program to channel"""
        if program not in self.programs:
            self.programs.append(program)
            # Keep programs sorted by start time
            self.programs.sort(key=lambda p: p.start_time)
    
    def get_current_program(self) -> Optional[EPGProgram]:
        """Get currently airing program"""
        now = datetime.now()
        for program in self.programs:
            if program.start_time <= now <= program.end_time:
                return program
        return None
    
    def get_next_program(self) -> Optional[EPGProgram]:
        """Get next upcoming program"""
        now = datetime.now()
        for program in self.programs:
            if program.start_time > now:
                return program
        return None
    
    def get_programs_for_date(self, date: datetime) -> List[EPGProgram]:
        """Get programs for a specific date"""
        start_of_day = date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_of_day = start_of_day + timedelta(days=1)
        
        return [
            program for program in self.programs
            if start_of_day <= program.start_time < end_of_day
        ]
    
    def get_programs_in_range(self, start: datetime, end: datetime) -> List[EPGProgram]:
        """Get programs within time range"""
        return [
            program for program in self.programs
            if not (program.end_time <= start or program.start_time >= end)
        ]
    
    def cleanup_old_programs(self, days_to_keep: int = 1) -> None:
        """Remove old programs to save space"""
        cutoff_time = datetime.now() - timedelta(days=days_to_keep)
        self.programs = [
            program for program in self.programs
            if program.end_time >= cutoff_time
        ]


@dataclass
class EPGData:
    """Container for all EPG data"""
    
    channels: Dict[str, EPGChannel] = field(default_factory=dict)
    last_updated: datetime = field(default_factory=datetime.now)
    
    def add_channel(self, channel_id: str) -> EPGChannel:
        """Add or get EPG channel"""
        if channel_id not in self.channels:
            self.channels[channel_id] = EPGChannel(channel_id=channel_id)
        return self.channels[channel_id]
    
    def get_channel(self, channel_id: str) -> Optional[EPGChannel]:
        """Get EPG channel"""
        return self.channels.get(channel_id)
    
    def cleanup_old_data(self, days_to_keep: int = 1) -> None:
        """Cleanup old EPG data"""
        for channel in self.channels.values():
            channel.cleanup_old_programs(days_to_keep)
