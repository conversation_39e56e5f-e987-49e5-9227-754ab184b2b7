#!/usr/bin/env python3
"""
Test script to identify and fix crash issues
"""

import sys
import os
import traceback

# Add src directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

def test_imports():
    """Test all imports to find crash source"""
    print("🔍 Testing imports to identify crash source...")
    
    try:
        print("1. Testing PyQt6 imports...")
        from PyQt6.QtWidgets import QApplication, QMainWindow
        print("   ✅ PyQt6 basic imports OK")
        
        print("2. Testing core imports...")
        from src.core.app_config import AppConfig
        from src.core.logger import setup_logger
        from src.core.database import DatabaseManager
        print("   ✅ Core imports OK")
        
        print("3. Testing UI imports...")
        from src.ui.main_window import MainWindow
        print("   ✅ MainWindow import OK")
        
        from src.ui.channel_list_widget import ChannelListWidget
        print("   ✅ ChannelListWidget import OK")
        
        print("4. Testing application creation...")
        app = QApplication([])
        print("   ✅ QApplication created OK")
        
        print("5. Testing config creation...")
        config = AppConfig()
        print("   ✅ AppConfig created OK")
        
        print("6. Testing database creation...")
        db = DatabaseManager()
        print("   ✅ DatabaseManager created OK")
        
        print("7. Testing main window creation...")
        # Create a mock app object
        class MockApp:
            def __init__(self):
                self.config = config
                self.db_manager = db
                self.cache_manager = None
                self.security_manager = None
                self.logger = setup_logger(__name__)
        
        mock_app = MockApp()
        main_window = MainWindow(mock_app)
        print("   ✅ MainWindow created OK")
        
        print("\n🎉 All tests passed! No crash detected in imports.")
        return True
        
    except Exception as e:
        print(f"\n❌ Crash detected at step: {e}")
        print("\nFull traceback:")
        traceback.print_exc()
        return False

def test_channel_list_widget():
    """Test ChannelListWidget specifically"""
    print("\n🔍 Testing ChannelListWidget functionality...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from src.ui.channel_list_widget import ChannelListWidget
        
        app = QApplication([])
        widget = ChannelListWidget()
        
        print("   ✅ ChannelListWidget created OK")
        
        # Test setting default limit
        widget.set_default_channel_limit(1000)
        print("   ✅ set_default_channel_limit OK")
        
        # Test simulate channels
        channels = widget.simulate_channels()
        print(f"   ✅ simulate_channels OK - {len(channels)} channels")
        
        # Test update categories
        widget.channels = channels
        widget.update_categories()
        print("   ✅ update_categories OK")
        
        print("\n🎉 ChannelListWidget tests passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ ChannelListWidget crash: {e}")
        traceback.print_exc()
        return False

def main():
    """Run crash detection tests"""
    print("🚨 IPTV Player Crash Detection & Fix")
    print("=" * 50)
    
    # Test 1: Imports
    if not test_imports():
        print("\n🔧 Fix needed: Import issues detected")
        return
    
    # Test 2: ChannelListWidget
    if not test_channel_list_widget():
        print("\n🔧 Fix needed: ChannelListWidget issues detected")
        return
    
    print("\n✅ No crash detected in tests!")
    print("The issue might be:")
    print("1. Runtime error during user interaction")
    print("2. Dialog/window focus issues")
    print("3. Event handling problems")
    print("4. Memory issues with large data")
    
    print("\n🔧 Recommended fixes applied:")
    print("- Added parent window checks for dialogs")
    print("- Added try-catch for dialog creation")
    print("- Improved error handling")
    print("- Fixed default values")
    
    print("\nTry running the application again!")

if __name__ == '__main__':
    main()
