"""
Database manager for IPTV Player using SQLite
"""

import sqlite3
import os
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any
from contextlib import contextmanager

from ..models.channel import Channel
from ..models.playlist import Playlist
from ..models.content import ContentInfo, Series, Season, Episode
from ..models.epg import EPGProgram, EPGChannel
from ..models.user_preferences import UserPreferences


class DatabaseManager:
    """Manages SQLite database operations"""
    
    def __init__(self, db_path: Optional[str] = None):
        if db_path is None:
            data_dir = Path.home() / '.iptv_player'
            data_dir.mkdir(exist_ok=True)
            db_path = str(data_dir / 'iptv_player.db')
        
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self._connection = None
    
    @contextmanager
    def get_connection(self):
        """Get database connection with context manager"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Enable dict-like access
        try:
            yield conn
        finally:
            conn.close()
    
    def initialize_database(self) -> None:
        """Initialize database with required tables"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Create playlists table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS playlists (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    playlist_type TEXT NOT NULL,
                    url TEXT,
                    file_path TEXT,
                    credentials TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    auto_refresh BOOLEAN DEFAULT 0,
                    refresh_interval INTEGER DEFAULT 3600,
                    last_updated TEXT,
                    channel_count INTEGER DEFAULT 0,
                    vod_count INTEGER DEFAULT 0,
                    series_count INTEGER DEFAULT 0,
                    metadata TEXT DEFAULT '{}',
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            ''')
            
            # Create channels table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS channels (
                    id TEXT PRIMARY KEY,
                    playlist_id TEXT,
                    name TEXT NOT NULL,
                    url TEXT NOT NULL,
                    group_name TEXT,
                    logo TEXT,
                    epg_id TEXT,
                    country TEXT,
                    language TEXT,
                    category TEXT,
                    is_favorite BOOLEAN DEFAULT 0,
                    is_hidden BOOLEAN DEFAULT 0,
                    is_locked BOOLEAN DEFAULT 0,
                    sort_order INTEGER DEFAULT 0,
                    metadata TEXT DEFAULT '{}',
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    FOREIGN KEY (playlist_id) REFERENCES playlists (id) ON DELETE CASCADE
                )
            ''')
            
            # Create content table (VOD, Series, Episodes)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS content (
                    id TEXT PRIMARY KEY,
                    playlist_id TEXT,
                    title TEXT NOT NULL,
                    content_type TEXT NOT NULL,
                    url TEXT NOT NULL,
                    description TEXT,
                    poster TEXT,
                    backdrop TEXT,
                    genre TEXT,
                    year INTEGER,
                    rating TEXT,
                    duration INTEGER,
                    language TEXT,
                    country TEXT,
                    director TEXT,
                    cast TEXT DEFAULT '[]',
                    is_favorite BOOLEAN DEFAULT 0,
                    is_watched BOOLEAN DEFAULT 0,
                    watch_progress REAL DEFAULT 0.0,
                    last_watched TEXT,
                    metadata TEXT DEFAULT '{}',
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    FOREIGN KEY (playlist_id) REFERENCES playlists (id) ON DELETE CASCADE
                )
            ''')
            
            # Create series table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS series (
                    id TEXT PRIMARY KEY,
                    content_id TEXT NOT NULL,
                    total_seasons INTEGER DEFAULT 0,
                    total_episodes INTEGER DEFAULT 0,
                    FOREIGN KEY (content_id) REFERENCES content (id) ON DELETE CASCADE
                )
            ''')
            
            # Create seasons table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS seasons (
                    id TEXT PRIMARY KEY,
                    series_id TEXT NOT NULL,
                    season_number INTEGER NOT NULL,
                    title TEXT,
                    poster TEXT,
                    year INTEGER,
                    FOREIGN KEY (series_id) REFERENCES series (id) ON DELETE CASCADE
                )
            ''')
            
            # Create episodes table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS episodes (
                    id TEXT PRIMARY KEY,
                    content_id TEXT NOT NULL,
                    series_id TEXT NOT NULL,
                    season_id TEXT NOT NULL,
                    season_number INTEGER NOT NULL,
                    episode_number INTEGER NOT NULL,
                    air_date TEXT,
                    FOREIGN KEY (content_id) REFERENCES content (id) ON DELETE CASCADE,
                    FOREIGN KEY (series_id) REFERENCES series (id) ON DELETE CASCADE,
                    FOREIGN KEY (season_id) REFERENCES seasons (id) ON DELETE CASCADE
                )
            ''')
            
            # Create EPG programs table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS epg_programs (
                    id TEXT PRIMARY KEY,
                    channel_id TEXT NOT NULL,
                    title TEXT NOT NULL,
                    start_time TEXT NOT NULL,
                    end_time TEXT NOT NULL,
                    description TEXT,
                    category TEXT,
                    genre TEXT,
                    rating TEXT,
                    episode_number INTEGER,
                    season_number INTEGER,
                    year INTEGER,
                    director TEXT,
                    cast TEXT DEFAULT '[]',
                    poster TEXT,
                    is_live BOOLEAN DEFAULT 0,
                    is_premiere BOOLEAN DEFAULT 0,
                    is_repeat BOOLEAN DEFAULT 0,
                    metadata TEXT DEFAULT '{}',
                    FOREIGN KEY (channel_id) REFERENCES channels (id) ON DELETE CASCADE
                )
            ''')
            
            # Create user preferences table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_preferences (
                    user_id TEXT PRIMARY KEY,
                    player_settings TEXT DEFAULT '{}',
                    ui_settings TEXT DEFAULT '{}',
                    security_settings TEXT DEFAULT '{}',
                    hidden_categories TEXT DEFAULT '[]',
                    favorite_channels TEXT DEFAULT '[]',
                    recently_watched TEXT DEFAULT '[]',
                    custom_channel_order TEXT DEFAULT '{}',
                    last_watched_channel TEXT,
                    auto_refresh_playlists BOOLEAN DEFAULT 1,
                    cache_enabled BOOLEAN DEFAULT 1,
                    cache_size_mb INTEGER DEFAULT 500,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            ''')
            
            # Create cache table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS cache (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    expires_at TEXT,
                    created_at TEXT NOT NULL
                )
            ''')
            
            # Create indexes for better performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_channels_playlist ON channels(playlist_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_channels_category ON channels(category)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_channels_favorite ON channels(is_favorite)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_content_playlist ON content(playlist_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_content_type ON content(content_type)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_epg_channel ON epg_programs(channel_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_epg_time ON epg_programs(start_time, end_time)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_cache_expires ON cache(expires_at)')
            
            conn.commit()
            self.logger.info("Database initialized successfully")
    
    def close(self) -> None:
        """Close database connection"""
        if self._connection:
            self._connection.close()
            self._connection = None
    
    # Playlist operations
    def save_playlist(self, playlist: Playlist) -> None:
        """Save playlist to database"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            data = playlist.to_dict()
            
            cursor.execute('''
                INSERT OR REPLACE INTO playlists 
                (id, name, playlist_type, url, file_path, credentials, is_active, 
                 auto_refresh, refresh_interval, last_updated, channel_count, 
                 vod_count, series_count, metadata, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                data['id'], data['name'], data['playlist_type'], data['url'],
                data['file_path'], str(data['credentials']) if data['credentials'] else None,
                data['is_active'], data['auto_refresh'], data['refresh_interval'],
                data['last_updated'], data['channel_count'], data['vod_count'],
                data['series_count'], data['metadata'], data['created_at'], data['updated_at']
            ))
            conn.commit()
    
    def get_playlist(self, playlist_id: str) -> Optional[Playlist]:
        """Get playlist by ID"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM playlists WHERE id = ?', (playlist_id,))
            row = cursor.fetchone()
            
            if row:
                return Playlist.from_dict(dict(row))
            return None
    
    def get_all_playlists(self) -> List[Playlist]:
        """Get all playlists"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM playlists ORDER BY name')
            rows = cursor.fetchall()
            
            return [Playlist.from_dict(dict(row)) for row in rows]
    
    def delete_playlist(self, playlist_id: str) -> None:
        """Delete playlist and all associated data"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM playlists WHERE id = ?', (playlist_id,))
            conn.commit()
    
    # Channel operations
    def save_channel(self, channel: Channel, playlist_id: str) -> None:
        """Save channel to database"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            data = channel.to_dict()
            
            cursor.execute('''
                INSERT OR REPLACE INTO channels 
                (id, playlist_id, name, url, group_name, logo, epg_id, country, 
                 language, category, is_favorite, is_hidden, is_locked, sort_order, 
                 metadata, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                data['id'], playlist_id, data['name'], data['url'], data['group'],
                data['logo'], data['epg_id'], data['country'], data['language'],
                data['category'], data['is_favorite'], data['is_hidden'],
                data['is_locked'], data['sort_order'], data['metadata'],
                data['created_at'], data['updated_at']
            ))
            conn.commit()
    
    def get_channels_by_playlist(self, playlist_id: str) -> List[Channel]:
        """Get all channels for a playlist"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM channels
                WHERE playlist_id = ?
                ORDER BY sort_order, name
            ''', (playlist_id,))
            rows = cursor.fetchall()

            return [Channel.from_dict(dict(row)) for row in rows]

    def get_channel(self, channel_id: str) -> Optional[Channel]:
        """Get channel by ID"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM channels WHERE id = ?', (channel_id,))
            row = cursor.fetchone()

            if row:
                return Channel.from_dict(dict(row))
            return None

    def get_favorite_channels(self) -> List[Channel]:
        """Get all favorite channels"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM channels
                WHERE is_favorite = 1
                ORDER BY name
            ''')
            rows = cursor.fetchall()

            return [Channel.from_dict(dict(row)) for row in rows]

    def update_channel_favorite(self, channel_id: str, is_favorite: bool) -> None:
        """Update channel favorite status"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE channels
                SET is_favorite = ?, updated_at = datetime('now')
                WHERE id = ?
            ''', (is_favorite, channel_id))
            conn.commit()

    def delete_channels_by_playlist(self, playlist_id: str) -> None:
        """Delete all channels for a playlist"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM channels WHERE playlist_id = ?', (playlist_id,))
            conn.commit()

    # Content operations (VOD, Series, Episodes)
    def save_content(self, content: ContentInfo, playlist_id: str) -> None:
        """Save content to database"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            data = content.to_dict()

            cursor.execute('''
                INSERT OR REPLACE INTO content
                (id, playlist_id, title, content_type, url, description, poster,
                 backdrop, genre, year, rating, duration, language, country,
                 director, cast, is_favorite, is_watched, watch_progress,
                 last_watched, metadata, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                data['id'], playlist_id, data['title'], data['content_type'],
                data['url'], data['description'], data['poster'], data['backdrop'],
                data['genre'], data['year'], data['rating'], data['duration'],
                data['language'], data['country'], data['director'], data['cast'],
                data['is_favorite'], data['is_watched'], data['watch_progress'],
                data['last_watched'], data['metadata'], data['created_at'], data['updated_at']
            ))
            conn.commit()

    def get_content_by_playlist(self, playlist_id: str, content_type: str = None) -> List[ContentInfo]:
        """Get content by playlist and optionally by type"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            if content_type:
                cursor.execute('''
                    SELECT * FROM content
                    WHERE playlist_id = ? AND content_type = ?
                    ORDER BY title
                ''', (playlist_id, content_type))
            else:
                cursor.execute('''
                    SELECT * FROM content
                    WHERE playlist_id = ?
                    ORDER BY content_type, title
                ''', (playlist_id,))

            rows = cursor.fetchall()
            return [ContentInfo.from_dict(dict(row)) for row in rows]

    def update_content_progress(self, content_id: str, progress: float, position: int = 0) -> None:
        """Update content watch progress"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            is_watched = progress >= 90.0

            cursor.execute('''
                UPDATE content
                SET watch_progress = ?, is_watched = ?, last_watched = datetime('now'),
                    updated_at = datetime('now')
                WHERE id = ?
            ''', (progress, is_watched, content_id))

            # Update metadata with position
            cursor.execute('''
                UPDATE content
                SET metadata = json_set(metadata, '$.last_position', ?)
                WHERE id = ?
            ''', (position, content_id))

            conn.commit()

    # EPG operations
    def save_epg_program(self, program: EPGProgram) -> None:
        """Save EPG program to database"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            data = program.to_dict()

            cursor.execute('''
                INSERT OR REPLACE INTO epg_programs
                (id, channel_id, title, start_time, end_time, description,
                 category, genre, rating, episode_number, season_number, year,
                 director, cast, poster, is_live, is_premiere, is_repeat, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                data['id'], data['channel_id'], data['title'], data['start_time'],
                data['end_time'], data['description'], data['category'], data['genre'],
                data['rating'], data['episode_number'], data['season_number'],
                data['year'], data['director'], data['cast'], data['poster'],
                data['is_live'], data['is_premiere'], data['is_repeat'], data['metadata']
            ))
            conn.commit()

    def get_epg_programs_for_channel(self, channel_id: str, start_time: str = None, end_time: str = None) -> List[EPGProgram]:
        """Get EPG programs for a channel within time range"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            if start_time and end_time:
                cursor.execute('''
                    SELECT * FROM epg_programs
                    WHERE channel_id = ? AND start_time >= ? AND end_time <= ?
                    ORDER BY start_time
                ''', (channel_id, start_time, end_time))
            else:
                cursor.execute('''
                    SELECT * FROM epg_programs
                    WHERE channel_id = ?
                    ORDER BY start_time
                ''', (channel_id,))

            rows = cursor.fetchall()
            return [EPGProgram.from_dict(dict(row)) for row in rows]

    def cleanup_old_epg(self, days_to_keep: int = 1) -> None:
        """Remove old EPG data"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                DELETE FROM epg_programs
                WHERE end_time < datetime('now', '-{} days')
            '''.format(days_to_keep))
            conn.commit()

    # User preferences operations
    def save_user_preferences(self, preferences: UserPreferences) -> None:
        """Save user preferences to database"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            data = preferences.to_dict()

            cursor.execute('''
                INSERT OR REPLACE INTO user_preferences
                (user_id, player_settings, ui_settings, security_settings,
                 hidden_categories, favorite_channels, recently_watched,
                 custom_channel_order, last_watched_channel, auto_refresh_playlists,
                 cache_enabled, cache_size_mb, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                data['user_id'], data['player_settings'], data['ui_settings'],
                data['security_settings'], data['hidden_categories'], data['favorite_channels'],
                data['recently_watched'], data['custom_channel_order'], data['last_watched_channel'],
                data['auto_refresh_playlists'], data['cache_enabled'], data['cache_size_mb'],
                data['created_at'], data['updated_at']
            ))
            conn.commit()

    def get_user_preferences(self, user_id: str = "default") -> UserPreferences:
        """Get user preferences"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM user_preferences WHERE user_id = ?', (user_id,))
            row = cursor.fetchone()

            if row:
                return UserPreferences.from_dict(dict(row))
            else:
                # Return default preferences
                preferences = UserPreferences(user_id=user_id)
                self.save_user_preferences(preferences)
                return preferences

    # Cache operations
    def set_cache(self, key: str, value: str, expires_at: str = None) -> None:
        """Set cache value"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO cache (key, value, expires_at, created_at)
                VALUES (?, ?, ?, datetime('now'))
            ''', (key, value, expires_at))
            conn.commit()

    def get_cache(self, key: str) -> Optional[str]:
        """Get cache value"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT value FROM cache
                WHERE key = ? AND (expires_at IS NULL OR expires_at > datetime('now'))
            ''', (key,))
            row = cursor.fetchone()

            return row[0] if row else None

    def cleanup_expired_cache(self) -> None:
        """Remove expired cache entries"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                DELETE FROM cache
                WHERE expires_at IS NOT NULL AND expires_at <= datetime('now')
            ''')
            conn.commit()
