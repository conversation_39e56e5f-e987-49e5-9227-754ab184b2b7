"""
M3U/M3U8 playlist parser for IPTV Player
"""

import re
import uuid
import logging
from typing import List, Dict, Any, Optional, Tuple
from urllib.parse import urljoin, urlparse
import requests

from ..models.channel import Channel, ChannelGroup
from ..models.playlist import Playlist, PlaylistType
from ..core.logger import LoggerMixin


class M3UParser(LoggerMixin):
    """Parser for M3U and M3U8 playlist formats"""
    
    def __init__(self):
        self.channels: List[Channel] = []
        self.groups: Dict[str, ChannelGroup] = {}
        
    def parse_file(self, file_path: str) -> Tuple[List[Channel], Dict[str, ChannelGroup]]:
        """Parse M3U file from local path"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return self.parse_content(content)
        except UnicodeDecodeError:
            # Try with different encodings
            for encoding in ['latin-1', 'cp1252', 'iso-8859-1']:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    return self.parse_content(content)
                except UnicodeDecodeError:
                    continue
            raise ValueError(f"Unable to decode file {file_path}")
    
    def parse_url(self, url: str, headers: Optional[Dict[str, str]] = None) -> Tuple[List[Channel], Dict[str, ChannelGroup]]:
        """Parse M3U from URL"""
        try:
            response = requests.get(url, headers=headers or {}, timeout=30)
            response.raise_for_status()
            
            # Try to decode with proper encoding
            content = response.text
            return self.parse_content(content, base_url=url)
            
        except requests.RequestException as e:
            self.logger.error(f"Failed to fetch playlist from {url}: {e}")
            raise
    
    def parse_content(self, content: str, base_url: Optional[str] = None) -> Tuple[List[Channel], Dict[str, ChannelGroup]]:
        """Parse M3U content string"""
        self.channels = []
        self.groups = {}
        
        lines = content.strip().split('\n')
        
        # Check if it's a valid M3U file
        if not lines or not lines[0].strip().startswith('#EXTM3U'):
            raise ValueError("Invalid M3U format: missing #EXTM3U header")
        
        current_extinf = None
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            if not line or line.startswith('#EXTM3U'):
                continue
            
            if line.startswith('#EXTINF:'):
                current_extinf = self._parse_extinf(line)
            elif line.startswith('#EXTGRP:'):
                # Handle group information
                if current_extinf:
                    current_extinf['group'] = line[8:].strip()
            elif line.startswith('#EXTVLCOPT:'):
                # Handle VLC options
                if current_extinf:
                    if 'vlc_options' not in current_extinf:
                        current_extinf['vlc_options'] = []
                    current_extinf['vlc_options'].append(line[11:].strip())
            elif not line.startswith('#'):
                # This is a URL line
                if current_extinf:
                    channel = self._create_channel(current_extinf, line, base_url)
                    if channel:
                        self.channels.append(channel)
                        self._add_to_group(channel)
                    current_extinf = None
                else:
                    # URL without EXTINF, create basic channel
                    channel = self._create_basic_channel(line, base_url)
                    if channel:
                        self.channels.append(channel)
                        self._add_to_group(channel)
        
        self.logger.info(f"Parsed {len(self.channels)} channels in {len(self.groups)} groups")
        return self.channels, self.groups
    
    def _parse_extinf(self, extinf_line: str) -> Dict[str, Any]:
        """Parse EXTINF line and extract metadata"""
        # Remove #EXTINF: prefix
        extinf_content = extinf_line[8:].strip()
        
        # Split duration and title/attributes
        parts = extinf_content.split(',', 1)
        if len(parts) < 2:
            return {'duration': -1, 'title': 'Unknown Channel'}
        
        duration_part = parts[0].strip()
        title_part = parts[1].strip()
        
        # Parse duration
        try:
            duration = float(duration_part.split()[0])
        except (ValueError, IndexError):
            duration = -1
        
        # Extract attributes from title part
        attributes = self._extract_attributes(title_part)
        
        # Extract channel name (everything after the last comma or the whole string)
        title = self._extract_title(title_part)
        
        return {
            'duration': duration,
            'title': title,
            'attributes': attributes
        }
    
    def _extract_attributes(self, text: str) -> Dict[str, str]:
        """Extract attributes from EXTINF title part"""
        attributes = {}
        
        # Common attribute patterns
        patterns = {
            'tvg-id': r'tvg-id="([^"]*)"',
            'tvg-name': r'tvg-name="([^"]*)"',
            'tvg-logo': r'tvg-logo="([^"]*)"',
            'tvg-url': r'tvg-url="([^"]*)"',
            'group-title': r'group-title="([^"]*)"',
            'tvg-country': r'tvg-country="([^"]*)"',
            'tvg-language': r'tvg-language="([^"]*)"',
            'tvg-shift': r'tvg-shift="([^"]*)"',
            'radio': r'radio="([^"]*)"',
            'tvg-rec': r'tvg-rec="([^"]*)"'
        }
        
        for attr_name, pattern in patterns.items():
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                attributes[attr_name] = match.group(1)
        
        return attributes
    
    def _extract_title(self, text: str) -> str:
        """Extract channel title from EXTINF title part"""
        # Remove all attributes first
        cleaned = re.sub(r'\w+="[^"]*"', '', text).strip()
        
        # Remove extra spaces
        cleaned = re.sub(r'\s+', ' ', cleaned)
        
        # If empty after cleaning, try to get from tvg-name
        if not cleaned:
            tvg_name_match = re.search(r'tvg-name="([^"]*)"', text, re.IGNORECASE)
            if tvg_name_match:
                return tvg_name_match.group(1)
            return "Unknown Channel"
        
        return cleaned
    
    def _create_channel(self, extinf_data: Dict[str, Any], url: str, base_url: Optional[str] = None) -> Optional[Channel]:
        """Create Channel object from parsed data"""
        try:
            # Resolve relative URLs
            if base_url and not url.startswith(('http://', 'https://', 'rtmp://', 'rtsp://')):
                url = urljoin(base_url, url)
            
            attributes = extinf_data.get('attributes', {})
            
            # Generate unique ID
            channel_id = attributes.get('tvg-id') or str(uuid.uuid4())
            
            # Create channel
            channel = Channel(
                id=channel_id,
                name=extinf_data['title'],
                url=url,
                group=attributes.get('group-title'),
                logo=attributes.get('tvg-logo'),
                epg_id=attributes.get('tvg-id'),
                country=attributes.get('tvg-country'),
                language=attributes.get('tvg-language'),
                category=attributes.get('group-title')
            )
            
            # Add additional metadata
            if 'vlc_options' in extinf_data:
                channel.metadata['vlc_options'] = extinf_data['vlc_options']
            
            if attributes.get('tvg-shift'):
                channel.metadata['time_shift'] = attributes['tvg-shift']
            
            if attributes.get('radio') == 'true':
                channel.metadata['is_radio'] = True
            
            return channel
            
        except Exception as e:
            self.logger.error(f"Failed to create channel from {extinf_data}: {e}")
            return None
    
    def _create_basic_channel(self, url: str, base_url: Optional[str] = None) -> Optional[Channel]:
        """Create basic channel from URL only"""
        try:
            # Resolve relative URLs
            if base_url and not url.startswith(('http://', 'https://', 'rtmp://', 'rtsp://')):
                url = urljoin(base_url, url)
            
            # Extract name from URL
            parsed_url = urlparse(url)
            name = parsed_url.path.split('/')[-1] or f"Channel {uuid.uuid4().hex[:8]}"
            
            return Channel(
                id=str(uuid.uuid4()),
                name=name,
                url=url
            )
            
        except Exception as e:
            self.logger.error(f"Failed to create basic channel from {url}: {e}")
            return None
    
    def _add_to_group(self, channel: Channel) -> None:
        """Add channel to appropriate group"""
        group_name = channel.group or "Uncategorized"
        
        if group_name not in self.groups:
            self.groups[group_name] = ChannelGroup(name=group_name)
        
        self.groups[group_name].add_channel(channel)
    
    def validate_playlist(self, content: str) -> bool:
        """Validate if content is a valid M3U playlist"""
        lines = content.strip().split('\n')
        
        if not lines:
            return False
        
        # Check for M3U header
        if not lines[0].strip().startswith('#EXTM3U'):
            return False
        
        # Check for at least one EXTINF or URL
        has_extinf = any(line.strip().startswith('#EXTINF:') for line in lines)
        has_url = any(not line.strip().startswith('#') and line.strip() for line in lines)
        
        return has_extinf or has_url
    
    def get_playlist_info(self, content: str) -> Dict[str, Any]:
        """Extract playlist metadata"""
        lines = content.strip().split('\n')
        info = {
            'format': 'M3U',
            'version': '1.0',
            'channel_count': 0,
            'group_count': 0,
            'has_epg': False,
            'has_logos': False
        }
        
        groups = set()
        channel_count = 0
        has_epg = False
        has_logos = False
        
        for line in lines:
            line = line.strip()
            
            if line.startswith('#EXTINF:'):
                channel_count += 1
                
                # Check for EPG info
                if 'tvg-id=' in line:
                    has_epg = True
                
                # Check for logos
                if 'tvg-logo=' in line:
                    has_logos = True
                
                # Extract group
                group_match = re.search(r'group-title="([^"]*)"', line, re.IGNORECASE)
                if group_match:
                    groups.add(group_match.group(1))
        
        info.update({
            'channel_count': channel_count,
            'group_count': len(groups),
            'has_epg': has_epg,
            'has_logos': has_logos
        })
        
        return info
