# دليل استخدام قوائم التشغيل - IPTV Player

## 🎯 كيفية إضافة قائمة تشغيل

### 1. **إضافة قائمة M3U محلية**
1. انقر على زر "Add Playlist" في شريط الأدوات
2. ا<PERSON><PERSON><PERSON> "M3U" من قائمة النوع
3. انق<PERSON> على "Browse" واختر ملف `.m3u` أو `.m3u8`
4. أدخل اسم للقائمة
5. انقر "OK"

### 2. **إضافة قائمة M3U من رابط**
1. انقر على زر "Add Playlist"
2. اختر "M3U8" من قائمة النوع
3. أدخل الرابط في حقل "URL/File"
4. أدخل اسم للقائمة
5. انقر "OK"

### 3. **إضافة Xtream Codes**
1. انقر على زر "Add Playlist"
2. <PERSON><PERSON><PERSON><PERSON> "XTREAM" من قائمة النوع
3. أدخل رابط الخادم في "Server URL"
4. أدخل اسم المستخدم وكلمة المرور
5. انقر "OK"

### 4. **إضافة Stalker Portal**
1. انقر على زر "Add Playlist"
2. اختر "STALKER" من قائمة النوع
3. أدخل رابط البوابة في "Portal URL"
4. أدخل عنوان MAC في اسم المستخدم
5. انقر "OK"

## 🧪 **اختبار سريع**

يمكنك اختبار التطبيق باستخدام ملف `test_playlist.m3u` المرفق:

1. شغل التطبيق: `python main.py`
2. انقر "Add Playlist"
3. اختر "M3U"
4. انقر "Browse" واختر `test_playlist.m3u`
5. أدخل اسم "Test Channels"
6. انقر "OK"

## ✅ **الميزات المتاحة الآن**

- ✅ تحليل قوائم M3U/M3U8 الحقيقية
- ✅ استخراج معلومات القنوات (الاسم، الشعار، الفئة)
- ✅ تصفية القنوات حسب الفئة
- ✅ البحث في القنوات
- ✅ إضافة للمفضلة
- ✅ حفظ قوائم التشغيل
- ✅ رسائل خطأ واضحة

## 🔧 **الميزات قيد التطوير**

- 🔄 دعم Xtream Codes API (يتطلب تنفيذ غير متزامن)
- 🔄 دعم Stalker Portal (يتطلب تنفيذ غير متزامن)
- 🔄 تشغيل الفيديو (يتطلب VLC)
- 🔄 دليل البرامج EPG

## 🐛 **استكشاف الأخطاء**

### إذا لم تظهر القنوات:
1. تأكد من صحة تنسيق ملف M3U
2. تحقق من صحة الروابط
3. راجع رسائل الخطأ في النافذة المنبثقة

### إذا فشل تحميل قائمة من رابط:
1. تأكد من اتصال الإنترنت
2. تحقق من صحة الرابط
3. تأكد من أن الخادم يدعم CORS

## 📝 **ملاحظات مهمة**

- التطبيق يدعم الآن تحليل قوائم M3U الحقيقية
- في حالة فشل التحليل، سيعرض قنوات تجريبية
- يتم حفظ قوائم التشغيل محلياً
- يمكن إضافة عدة قوائم تشغيل

## 🎉 **نجح الإصلاح!**

تم إصلاح مشكلة قوائم التشغيل وأصبح التطبيق يعمل بشكل صحيح مع:
- تحليل حقيقي لملفات M3U
- استخراج معلومات القنوات
- عرض القنوات في الواجهة
- إدارة الأخطاء بشكل صحيح
