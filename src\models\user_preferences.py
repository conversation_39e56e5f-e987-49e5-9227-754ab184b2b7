"""
User preferences and settings data models
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum
import json


class PlayerBackend(Enum):
    """Video player backend options"""
    VLC = "vlc"
    EXOPLAYER = "exoplayer"
    SYSTEM_DEFAULT = "system_default"


class SortOrder(Enum):
    """Channel sorting options"""
    NAME_ASC = "name_asc"
    NAME_DESC = "name_desc"
    GROUP_ASC = "group_asc"
    GROUP_DESC = "group_desc"
    CUSTOM = "custom"
    RECENTLY_WATCHED = "recently_watched"
    FAVORITES_FIRST = "favorites_first"


class ViewMode(Enum):
    """Channel view modes"""
    LIST = "list"
    GRID = "grid"
    COMPACT = "compact"


@dataclass
class PlayerSettings:
    """Video player settings"""
    
    backend: PlayerBackend = PlayerBackend.VLC
    auto_play: bool = True
    remember_position: bool = True
    auto_fullscreen: bool = False
    volume: float = 0.8
    subtitle_enabled: bool = False
    subtitle_size: int = 16
    subtitle_color: str = "#FFFFFF"
    buffer_size: int = 5000  # milliseconds
    network_timeout: int = 30  # seconds
    hardware_acceleration: bool = True
    aspect_ratio: str = "auto"  # auto, 16:9, 4:3, etc.
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'backend': self.backend.value,
            'auto_play': self.auto_play,
            'remember_position': self.remember_position,
            'auto_fullscreen': self.auto_fullscreen,
            'volume': self.volume,
            'subtitle_enabled': self.subtitle_enabled,
            'subtitle_size': self.subtitle_size,
            'subtitle_color': self.subtitle_color,
            'buffer_size': self.buffer_size,
            'network_timeout': self.network_timeout,
            'hardware_acceleration': self.hardware_acceleration,
            'aspect_ratio': self.aspect_ratio
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PlayerSettings':
        """Create from dictionary"""
        return cls(
            backend=PlayerBackend(data.get('backend', PlayerBackend.VLC.value)),
            auto_play=data.get('auto_play', True),
            remember_position=data.get('remember_position', True),
            auto_fullscreen=data.get('auto_fullscreen', False),
            volume=data.get('volume', 0.8),
            subtitle_enabled=data.get('subtitle_enabled', False),
            subtitle_size=data.get('subtitle_size', 16),
            subtitle_color=data.get('subtitle_color', '#FFFFFF'),
            buffer_size=data.get('buffer_size', 5000),
            network_timeout=data.get('network_timeout', 30),
            hardware_acceleration=data.get('hardware_acceleration', True),
            aspect_ratio=data.get('aspect_ratio', 'auto')
        )


@dataclass
class UISettings:
    """User interface settings"""
    
    theme: str = "dark"  # dark, light, auto
    primary_color: str = "#2196F3"
    accent_color: str = "#FFC107"
    view_mode: ViewMode = ViewMode.LIST
    sort_order: SortOrder = SortOrder.NAME_ASC
    show_channel_numbers: bool = True
    show_channel_logos: bool = True
    show_epg_info: bool = True
    grid_columns: int = 4
    compact_mode: bool = False
    font_size: str = "medium"  # small, medium, large
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'theme': self.theme,
            'primary_color': self.primary_color,
            'accent_color': self.accent_color,
            'view_mode': self.view_mode.value,
            'sort_order': self.sort_order.value,
            'show_channel_numbers': self.show_channel_numbers,
            'show_channel_logos': self.show_channel_logos,
            'show_epg_info': self.show_epg_info,
            'grid_columns': self.grid_columns,
            'compact_mode': self.compact_mode,
            'font_size': self.font_size
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UISettings':
        """Create from dictionary"""
        return cls(
            theme=data.get('theme', 'dark'),
            primary_color=data.get('primary_color', '#2196F3'),
            accent_color=data.get('accent_color', '#FFC107'),
            view_mode=ViewMode(data.get('view_mode', ViewMode.LIST.value)),
            sort_order=SortOrder(data.get('sort_order', SortOrder.NAME_ASC.value)),
            show_channel_numbers=data.get('show_channel_numbers', True),
            show_channel_logos=data.get('show_channel_logos', True),
            show_epg_info=data.get('show_epg_info', True),
            grid_columns=data.get('grid_columns', 4),
            compact_mode=data.get('compact_mode', False),
            font_size=data.get('font_size', 'medium')
        )


@dataclass
class SecuritySettings:
    """Security and parental control settings"""
    
    parental_control_enabled: bool = False
    parental_pin: Optional[str] = None
    locked_categories: List[str] = field(default_factory=list)
    content_rating_limit: Optional[str] = None
    require_pin_for_settings: bool = False
    auto_lock_timeout: int = 0  # minutes, 0 = disabled
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'parental_control_enabled': self.parental_control_enabled,
            'parental_pin': self.parental_pin,
            'locked_categories': json.dumps(self.locked_categories),
            'content_rating_limit': self.content_rating_limit,
            'require_pin_for_settings': self.require_pin_for_settings,
            'auto_lock_timeout': self.auto_lock_timeout
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SecuritySettings':
        """Create from dictionary"""
        locked_categories = data.get('locked_categories', '[]')
        if isinstance(locked_categories, str):
            locked_categories = json.loads(locked_categories)
        
        return cls(
            parental_control_enabled=data.get('parental_control_enabled', False),
            parental_pin=data.get('parental_pin'),
            locked_categories=locked_categories,
            content_rating_limit=data.get('content_rating_limit'),
            require_pin_for_settings=data.get('require_pin_for_settings', False),
            auto_lock_timeout=data.get('auto_lock_timeout', 0)
        )


@dataclass
class UserPreferences:
    """Main user preferences container"""
    
    user_id: str = "default"
    player_settings: PlayerSettings = field(default_factory=PlayerSettings)
    ui_settings: UISettings = field(default_factory=UISettings)
    security_settings: SecuritySettings = field(default_factory=SecuritySettings)
    hidden_categories: List[str] = field(default_factory=list)
    favorite_channels: List[str] = field(default_factory=list)
    recently_watched: List[str] = field(default_factory=list)
    custom_channel_order: Dict[str, int] = field(default_factory=dict)
    last_watched_channel: Optional[str] = None
    auto_refresh_playlists: bool = True
    cache_enabled: bool = True
    cache_size_mb: int = 500
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'user_id': self.user_id,
            'player_settings': json.dumps(self.player_settings.to_dict()),
            'ui_settings': json.dumps(self.ui_settings.to_dict()),
            'security_settings': json.dumps(self.security_settings.to_dict()),
            'hidden_categories': json.dumps(self.hidden_categories),
            'favorite_channels': json.dumps(self.favorite_channels),
            'recently_watched': json.dumps(self.recently_watched),
            'custom_channel_order': json.dumps(self.custom_channel_order),
            'last_watched_channel': self.last_watched_channel,
            'auto_refresh_playlists': self.auto_refresh_playlists,
            'cache_enabled': self.cache_enabled,
            'cache_size_mb': self.cache_size_mb,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserPreferences':
        """Create from dictionary"""
        # Parse JSON fields
        player_settings_data = data.get('player_settings', '{}')
        if isinstance(player_settings_data, str):
            player_settings_data = json.loads(player_settings_data)
        
        ui_settings_data = data.get('ui_settings', '{}')
        if isinstance(ui_settings_data, str):
            ui_settings_data = json.loads(ui_settings_data)
        
        security_settings_data = data.get('security_settings', '{}')
        if isinstance(security_settings_data, str):
            security_settings_data = json.loads(security_settings_data)
        
        hidden_categories = data.get('hidden_categories', '[]')
        if isinstance(hidden_categories, str):
            hidden_categories = json.loads(hidden_categories)
        
        favorite_channels = data.get('favorite_channels', '[]')
        if isinstance(favorite_channels, str):
            favorite_channels = json.loads(favorite_channels)
        
        recently_watched = data.get('recently_watched', '[]')
        if isinstance(recently_watched, str):
            recently_watched = json.loads(recently_watched)
        
        custom_channel_order = data.get('custom_channel_order', '{}')
        if isinstance(custom_channel_order, str):
            custom_channel_order = json.loads(custom_channel_order)
        
        return cls(
            user_id=data.get('user_id', 'default'),
            player_settings=PlayerSettings.from_dict(player_settings_data),
            ui_settings=UISettings.from_dict(ui_settings_data),
            security_settings=SecuritySettings.from_dict(security_settings_data),
            hidden_categories=hidden_categories,
            favorite_channels=favorite_channels,
            recently_watched=recently_watched,
            custom_channel_order=custom_channel_order,
            last_watched_channel=data.get('last_watched_channel'),
            auto_refresh_playlists=data.get('auto_refresh_playlists', True),
            cache_enabled=data.get('cache_enabled', True),
            cache_size_mb=data.get('cache_size_mb', 500),
            created_at=datetime.fromisoformat(data.get('created_at', datetime.now().isoformat())),
            updated_at=datetime.fromisoformat(data.get('updated_at', datetime.now().isoformat()))
        )
    
    def add_to_recently_watched(self, channel_id: str, max_items: int = 50) -> None:
        """Add channel to recently watched list"""
        if channel_id in self.recently_watched:
            self.recently_watched.remove(channel_id)
        
        self.recently_watched.insert(0, channel_id)
        
        # Keep only the most recent items
        if len(self.recently_watched) > max_items:
            self.recently_watched = self.recently_watched[:max_items]
        
        self.updated_at = datetime.now()
    
    def toggle_favorite(self, channel_id: str) -> bool:
        """Toggle channel favorite status, returns new status"""
        if channel_id in self.favorite_channels:
            self.favorite_channels.remove(channel_id)
            self.updated_at = datetime.now()
            return False
        else:
            self.favorite_channels.append(channel_id)
            self.updated_at = datetime.now()
            return True
