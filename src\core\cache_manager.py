"""
Cache manager for IPTV Player
Handles caching of images, EPG data, and other resources for improved performance
"""

import os
import json
import time
import hashlib
import asyncio
import aiohttp
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from pathlib import Path
import sqlite3

from .logger import LoggerMixin
from .app_config import get_config


class CacheManager(LoggerMixin):
    """Manages local caching for improved performance"""
    
    def __init__(self):
        self.config = get_config()
        self.cache_dir = Path(self.config.cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Cache subdirectories
        self.image_cache_dir = self.cache_dir / 'images'
        self.data_cache_dir = self.cache_dir / 'data'
        self.epg_cache_dir = self.cache_dir / 'epg'
        
        for cache_subdir in [self.image_cache_dir, self.data_cache_dir, self.epg_cache_dir]:
            cache_subdir.mkdir(exist_ok=True)
        
        # Cache database for metadata
        self.cache_db_path = self.cache_dir / 'cache_metadata.db'
        self.init_cache_database()
        
        # Memory cache for frequently accessed data
        self.memory_cache = {}
        self.memory_cache_ttl = {}
        self.max_memory_cache_size = 100  # Maximum items in memory cache
    
    def init_cache_database(self):
        """Initialize cache metadata database"""
        with sqlite3.connect(self.cache_db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS cache_entries (
                    key TEXT PRIMARY KEY,
                    file_path TEXT NOT NULL,
                    created_at TIMESTAMP NOT NULL,
                    expires_at TIMESTAMP,
                    size_bytes INTEGER,
                    access_count INTEGER DEFAULT 0,
                    last_accessed TIMESTAMP
                )
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_expires_at ON cache_entries(expires_at)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_last_accessed ON cache_entries(last_accessed)
            ''')
            
            conn.commit()
    
    def _generate_cache_key(self, url: str, cache_type: str = 'default') -> str:
        """Generate cache key from URL"""
        key_data = f"{cache_type}:{url}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _get_cache_file_path(self, cache_key: str, cache_type: str, extension: str = '') -> Path:
        """Get cache file path for a key"""
        if cache_type == 'image':
            cache_dir = self.image_cache_dir
        elif cache_type == 'epg':
            cache_dir = self.epg_cache_dir
        else:
            cache_dir = self.data_cache_dir
        
        filename = f"{cache_key}{extension}"
        return cache_dir / filename
    
    def is_cached(self, key: str) -> bool:
        """Check if item is cached and not expired"""
        # Check memory cache first
        if key in self.memory_cache:
            if key in self.memory_cache_ttl:
                if datetime.now() > self.memory_cache_ttl[key]:
                    del self.memory_cache[key]
                    del self.memory_cache_ttl[key]
                    return False
            return True
        
        # Check file cache
        with sqlite3.connect(self.cache_db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT file_path, expires_at FROM cache_entries 
                WHERE key = ?
            ''', (key,))
            
            result = cursor.fetchone()
            if not result:
                return False
            
            file_path, expires_at = result
            
            # Check if file exists
            if not os.path.exists(file_path):
                self._remove_cache_entry(key)
                return False
            
            # Check expiration
            if expires_at:
                expires_datetime = datetime.fromisoformat(expires_at)
                if datetime.now() > expires_datetime:
                    self._remove_cache_entry(key)
                    return False
            
            return True
    
    def get_cached_data(self, key: str) -> Optional[Any]:
        """Get cached data"""
        # Check memory cache first
        if key in self.memory_cache:
            if key in self.memory_cache_ttl:
                if datetime.now() > self.memory_cache_ttl[key]:
                    del self.memory_cache[key]
                    del self.memory_cache_ttl[key]
                    return None
            return self.memory_cache[key]
        
        # Check file cache
        with sqlite3.connect(self.cache_db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT file_path, expires_at FROM cache_entries 
                WHERE key = ?
            ''', (key,))
            
            result = cursor.fetchone()
            if not result:
                return None
            
            file_path, expires_at = result
            
            # Check if file exists
            if not os.path.exists(file_path):
                self._remove_cache_entry(key)
                return None
            
            # Check expiration
            if expires_at:
                expires_datetime = datetime.fromisoformat(expires_at)
                if datetime.now() > expires_datetime:
                    self._remove_cache_entry(key)
                    return None
            
            # Update access statistics
            cursor.execute('''
                UPDATE cache_entries 
                SET access_count = access_count + 1, last_accessed = ?
                WHERE key = ?
            ''', (datetime.now().isoformat(), key))
            conn.commit()
            
            # Load data from file
            try:
                if file_path.endswith('.json'):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        return json.load(f)
                else:
                    with open(file_path, 'rb') as f:
                        return f.read()
            except Exception as e:
                self.logger.error(f"Failed to load cached data from {file_path}: {e}")
                self._remove_cache_entry(key)
                return None
    
    def cache_data(self, key: str, data: Any, cache_type: str = 'data', 
                   ttl_hours: int = 24, extension: str = '.json'):
        """Cache data to file and optionally memory"""
        try:
            # Determine file path
            file_path = self._get_cache_file_path(key, cache_type, extension)
            
            # Save to file
            if extension == '.json':
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
            else:
                with open(file_path, 'wb') as f:
                    f.write(data)
            
            # Calculate expiration
            expires_at = None
            if ttl_hours > 0:
                expires_at = (datetime.now() + timedelta(hours=ttl_hours)).isoformat()
            
            # Update database
            file_size = os.path.getsize(file_path)
            with sqlite3.connect(self.cache_db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO cache_entries 
                    (key, file_path, created_at, expires_at, size_bytes, last_accessed)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    key, str(file_path), datetime.now().isoformat(),
                    expires_at, file_size, datetime.now().isoformat()
                ))
                conn.commit()
            
            # Cache in memory if it's small enough and frequently accessed
            if cache_type == 'data' and len(str(data)) < 10000:  # Less than 10KB
                self._cache_in_memory(key, data, ttl_hours)
            
            self.logger.debug(f"Cached data with key: {key}")
            
        except Exception as e:
            self.logger.error(f"Failed to cache data with key {key}: {e}")
    
    def _cache_in_memory(self, key: str, data: Any, ttl_hours: int):
        """Cache data in memory"""
        # Remove oldest items if cache is full
        if len(self.memory_cache) >= self.max_memory_cache_size:
            oldest_key = min(self.memory_cache_ttl.keys(), 
                           key=lambda k: self.memory_cache_ttl.get(k, datetime.min))
            del self.memory_cache[oldest_key]
            del self.memory_cache_ttl[oldest_key]
        
        self.memory_cache[key] = data
        if ttl_hours > 0:
            self.memory_cache_ttl[key] = datetime.now() + timedelta(hours=ttl_hours)
    
    async def cache_image_from_url(self, url: str, ttl_hours: int = 168) -> Optional[str]:
        """Download and cache image from URL"""
        if not url:
            return None
        
        cache_key = self._generate_cache_key(url, 'image')
        
        # Check if already cached
        if self.is_cached(cache_key):
            cached_path = self._get_cache_file_path(cache_key, 'image')
            return str(cached_path)
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status == 200:
                        content = await response.read()
                        
                        # Determine file extension from content type
                        content_type = response.headers.get('content-type', '')
                        if 'jpeg' in content_type or 'jpg' in content_type:
                            extension = '.jpg'
                        elif 'png' in content_type:
                            extension = '.png'
                        elif 'gif' in content_type:
                            extension = '.gif'
                        elif 'webp' in content_type:
                            extension = '.webp'
                        else:
                            extension = '.jpg'  # Default
                        
                        # Cache the image
                        self.cache_data(cache_key, content, 'image', ttl_hours, extension)
                        
                        cached_path = self._get_cache_file_path(cache_key, 'image', extension)
                        return str(cached_path)
                    
        except Exception as e:
            self.logger.error(f"Failed to cache image from {url}: {e}")
        
        return None
    
    def cache_epg_data(self, channel_id: str, epg_data: List[Dict[str, Any]], ttl_hours: int = 6):
        """Cache EPG data for a channel"""
        cache_key = f"epg_{channel_id}"
        self.cache_data(cache_key, epg_data, 'epg', ttl_hours)
    
    def get_cached_epg_data(self, channel_id: str) -> Optional[List[Dict[str, Any]]]:
        """Get cached EPG data for a channel"""
        cache_key = f"epg_{channel_id}"
        return self.get_cached_data(cache_key)
    
    def cache_playlist_data(self, playlist_id: str, playlist_data: Dict[str, Any], ttl_hours: int = 24):
        """Cache playlist data"""
        cache_key = f"playlist_{playlist_id}"
        self.cache_data(cache_key, playlist_data, 'data', ttl_hours)
    
    def get_cached_playlist_data(self, playlist_id: str) -> Optional[Dict[str, Any]]:
        """Get cached playlist data"""
        cache_key = f"playlist_{playlist_id}"
        return self.get_cached_data(cache_key)
    
    def _remove_cache_entry(self, key: str):
        """Remove cache entry from database and file system"""
        with sqlite3.connect(self.cache_db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT file_path FROM cache_entries WHERE key = ?', (key,))
            result = cursor.fetchone()
            
            if result:
                file_path = result[0]
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                except Exception as e:
                    self.logger.error(f"Failed to remove cache file {file_path}: {e}")
                
                cursor.execute('DELETE FROM cache_entries WHERE key = ?', (key,))
                conn.commit()
        
        # Remove from memory cache
        if key in self.memory_cache:
            del self.memory_cache[key]
        if key in self.memory_cache_ttl:
            del self.memory_cache_ttl[key]
    
    def cleanup_expired_cache(self):
        """Remove expired cache entries"""
        current_time = datetime.now().isoformat()
        
        with sqlite3.connect(self.cache_db_path) as conn:
            cursor = conn.cursor()
            
            # Get expired entries
            cursor.execute('''
                SELECT key, file_path FROM cache_entries 
                WHERE expires_at IS NOT NULL AND expires_at < ?
            ''', (current_time,))
            
            expired_entries = cursor.fetchall()
            
            # Remove expired files
            for key, file_path in expired_entries:
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                except Exception as e:
                    self.logger.error(f"Failed to remove expired cache file {file_path}: {e}")
            
            # Remove from database
            cursor.execute('''
                DELETE FROM cache_entries 
                WHERE expires_at IS NOT NULL AND expires_at < ?
            ''', (current_time,))
            
            conn.commit()
            
            if expired_entries:
                self.logger.info(f"Cleaned up {len(expired_entries)} expired cache entries")
    
    def cleanup_cache_by_size(self, max_size_mb: int):
        """Remove least recently used cache entries to stay under size limit"""
        max_size_bytes = max_size_mb * 1024 * 1024
        
        with sqlite3.connect(self.cache_db_path) as conn:
            cursor = conn.cursor()
            
            # Get total cache size
            cursor.execute('SELECT SUM(size_bytes) FROM cache_entries')
            total_size = cursor.fetchone()[0] or 0
            
            if total_size <= max_size_bytes:
                return
            
            # Get entries ordered by last access (oldest first)
            cursor.execute('''
                SELECT key, file_path, size_bytes FROM cache_entries 
                ORDER BY last_accessed ASC
            ''')
            
            entries = cursor.fetchall()
            removed_size = 0
            removed_count = 0
            
            for key, file_path, size_bytes in entries:
                if total_size - removed_size <= max_size_bytes:
                    break
                
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                    
                    cursor.execute('DELETE FROM cache_entries WHERE key = ?', (key,))
                    removed_size += size_bytes
                    removed_count += 1
                    
                except Exception as e:
                    self.logger.error(f"Failed to remove cache file {file_path}: {e}")
            
            conn.commit()
            
            if removed_count > 0:
                self.logger.info(f"Removed {removed_count} cache entries ({removed_size / 1024 / 1024:.1f} MB)")
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """Get cache usage statistics"""
        with sqlite3.connect(self.cache_db_path) as conn:
            cursor = conn.cursor()
            
            # Total entries and size
            cursor.execute('SELECT COUNT(*), SUM(size_bytes) FROM cache_entries')
            total_entries, total_size = cursor.fetchone()
            total_size = total_size or 0
            
            # Entries by type
            cursor.execute('''
                SELECT 
                    CASE 
                        WHEN file_path LIKE '%/images/%' THEN 'images'
                        WHEN file_path LIKE '%/epg/%' THEN 'epg'
                        ELSE 'data'
                    END as cache_type,
                    COUNT(*),
                    SUM(size_bytes)
                FROM cache_entries
                GROUP BY cache_type
            ''')
            
            type_stats = {}
            for cache_type, count, size in cursor.fetchall():
                type_stats[cache_type] = {
                    'count': count,
                    'size_bytes': size or 0
                }
            
            return {
                'total_entries': total_entries or 0,
                'total_size_bytes': total_size,
                'total_size_mb': total_size / 1024 / 1024,
                'memory_cache_entries': len(self.memory_cache),
                'type_breakdown': type_stats
            }
    
    def clear_all_cache(self):
        """Clear all cached data"""
        try:
            # Clear memory cache
            self.memory_cache.clear()
            self.memory_cache_ttl.clear()
            
            # Remove all cache files
            with sqlite3.connect(self.cache_db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT file_path FROM cache_entries')
                
                for (file_path,) in cursor.fetchall():
                    try:
                        if os.path.exists(file_path):
                            os.remove(file_path)
                    except Exception as e:
                        self.logger.error(f"Failed to remove cache file {file_path}: {e}")
                
                # Clear database
                cursor.execute('DELETE FROM cache_entries')
                conn.commit()
            
            self.logger.info("Cleared all cache data")
            
        except Exception as e:
            self.logger.error(f"Failed to clear cache: {e}")


# Global cache manager instance
_cache_manager_instance = None


def get_cache_manager() -> CacheManager:
    """Get global cache manager instance"""
    global _cache_manager_instance
    if _cache_manager_instance is None:
        _cache_manager_instance = CacheManager()
    return _cache_manager_instance
