#!/usr/bin/env python3
"""
Debug test for IPTV Player
"""

import sys
import os
import traceback

# Add src directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

print("Starting debug test...")

try:
    print("Testing basic imports...")
    from PyQt6.QtWidgets import QApplication
    print("✓ PyQt6 import successful")
    
    print("Testing core imports...")
    from src.core.app_config import AppConfig
    print("✓ AppConfig import successful")
    
    from src.core.logger import setup_logger
    print("✓ Logger import successful")
    
    from src.core.database import DatabaseManager
    print("✓ DatabaseManager import successful")
    
    print("Testing UI imports...")
    from src.ui.main_window import MainWindow
    print("✓ MainWindow import successful")
    
    from src.ui.channel_list_widget import ChannelListWidget
    print("✓ ChannelListWidget import successful")
    
    print("Testing parser imports...")
    from src.parsers.m3u_parser import M3UParser
    print("✓ M3UParser import successful")
    
    print("Testing provider imports...")
    from src.providers.xtream_provider import XtreamProvider
    print("✓ XtreamProvider import successful")
    
    from src.providers.stalker_provider import StalkerProvider
    print("✓ StalkerProvider import successful")
    
    print("\n🎉 All imports successful!")
    print("The issue might be in the application initialization...")
    
    # Try to create QApplication
    print("\nTesting QApplication creation...")
    app = QApplication([])
    print("✓ QApplication created successfully")
    
    # Try to create config
    print("Testing configuration...")
    config = AppConfig()
    print("✓ Configuration created successfully")
    
    print("\n✅ Debug test completed successfully!")
    print("The application components are working correctly.")
    
except Exception as e:
    print(f"\n❌ Error occurred: {e}")
    print("\nFull traceback:")
    traceback.print_exc()
    sys.exit(1)
