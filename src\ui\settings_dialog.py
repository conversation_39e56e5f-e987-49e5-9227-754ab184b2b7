"""
Settings Dialog for IPTV Player (PyQt6)
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
    QWidget, QFormLayout, QLabel, QLineEdit, QSpinBox,
    QCheckBox, QComboBox, QSlider, QPushButton,
    QDialogButtonBox, QGroupBox, QFileDialog,
    QMessageBox, QTextEdit
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

from ..core.logger import LoggerMixin


class SettingsDialog(QDialog, LoggerMixin):
    """Settings dialog for application configuration"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.setWindowTitle("Settings")
        self.setModal(True)
        self.resize(500, 400)
        
        self.settings = self.load_settings()
        
        self.init_ui()
        self.populate_settings()
        
        self.logger.info("Settings dialog initialized")
    
    def init_ui(self):
        """Initialize the dialog UI"""
        layout = QVBoxLayout(self)
        
        # Tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create tabs
        self.create_general_tab()
        self.create_player_tab()
        self.create_network_tab()
        self.create_appearance_tab()
        self.create_advanced_tab()
        
        # Buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | 
            QDialogButtonBox.StandardButton.Cancel |
            QDialogButtonBox.StandardButton.Apply |
            QDialogButtonBox.StandardButton.RestoreDefaults
        )
        
        button_box.accepted.connect(self.accept_settings)
        button_box.rejected.connect(self.reject)
        button_box.button(QDialogButtonBox.StandardButton.Apply).clicked.connect(self.apply_settings)
        button_box.button(QDialogButtonBox.StandardButton.RestoreDefaults).clicked.connect(self.restore_defaults)
        
        layout.addWidget(button_box)
    
    def create_general_tab(self):
        """Create general settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Application settings
        app_group = QGroupBox("Application")
        app_layout = QFormLayout(app_group)
        
        self.startup_playlist_combo = QComboBox()
        self.startup_playlist_combo.addItems(["None", "Last Used", "Default"])
        app_layout.addRow("Startup Playlist:", self.startup_playlist_combo)
        
        self.minimize_to_tray_check = QCheckBox("Minimize to system tray")
        app_layout.addRow(self.minimize_to_tray_check)
        
        self.auto_update_check = QCheckBox("Check for updates automatically")
        app_layout.addRow(self.auto_update_check)
        
        layout.addWidget(app_group)
        
        # Data settings
        data_group = QGroupBox("Data & Storage")
        data_layout = QFormLayout(data_group)
        
        data_dir_layout = QHBoxLayout()
        self.data_dir_edit = QLineEdit()
        self.data_dir_edit.setReadOnly(True)
        data_dir_layout.addWidget(self.data_dir_edit)
        
        browse_data_btn = QPushButton("Browse")
        browse_data_btn.clicked.connect(self.browse_data_directory)
        data_dir_layout.addWidget(browse_data_btn)
        
        data_layout.addRow("Data Directory:", data_dir_layout)
        
        self.cache_size_spin = QSpinBox()
        self.cache_size_spin.setRange(50, 5000)
        self.cache_size_spin.setSuffix(" MB")
        data_layout.addRow("Cache Size:", self.cache_size_spin)
        
        layout.addWidget(data_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "General")
    
    def create_player_tab(self):
        """Create player settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Video settings
        video_group = QGroupBox("Video")
        video_layout = QFormLayout(video_group)
        
        self.video_backend_combo = QComboBox()
        self.video_backend_combo.addItems(["VLC", "MPV", "GStreamer"])
        video_layout.addRow("Video Backend:", self.video_backend_combo)
        
        self.hardware_accel_check = QCheckBox("Enable hardware acceleration")
        video_layout.addRow(self.hardware_accel_check)
        
        self.deinterlace_check = QCheckBox("Enable deinterlacing")
        video_layout.addRow(self.deinterlace_check)
        
        layout.addWidget(video_group)
        
        # Audio settings
        audio_group = QGroupBox("Audio")
        audio_layout = QFormLayout(audio_group)
        
        self.audio_device_combo = QComboBox()
        self.audio_device_combo.addItems(["Default", "System Default"])
        audio_layout.addRow("Audio Device:", self.audio_device_combo)
        
        self.default_volume_slider = QSlider(Qt.Orientation.Horizontal)
        self.default_volume_slider.setRange(0, 100)
        self.default_volume_slider.setValue(70)
        audio_layout.addRow("Default Volume:", self.default_volume_slider)
        
        layout.addWidget(audio_group)
        
        # Playback settings
        playback_group = QGroupBox("Playback")
        playback_layout = QFormLayout(playback_group)
        
        self.buffer_size_spin = QSpinBox()
        self.buffer_size_spin.setRange(1000, 30000)
        self.buffer_size_spin.setSuffix(" ms")
        playback_layout.addRow("Buffer Size:", self.buffer_size_spin)
        
        self.reconnect_attempts_spin = QSpinBox()
        self.reconnect_attempts_spin.setRange(0, 10)
        playback_layout.addRow("Reconnect Attempts:", self.reconnect_attempts_spin)
        
        layout.addWidget(playback_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Player")
    
    def create_network_tab(self):
        """Create network settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Connection settings
        connection_group = QGroupBox("Connection")
        connection_layout = QFormLayout(connection_group)
        
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(5, 120)
        self.timeout_spin.setSuffix(" seconds")
        connection_layout.addRow("Connection Timeout:", self.timeout_spin)
        
        self.user_agent_edit = QLineEdit()
        connection_layout.addRow("User Agent:", self.user_agent_edit)
        
        layout.addWidget(connection_group)
        
        # Proxy settings
        proxy_group = QGroupBox("Proxy")
        proxy_layout = QFormLayout(proxy_group)
        
        self.proxy_enabled_check = QCheckBox("Enable proxy")
        proxy_layout.addRow(self.proxy_enabled_check)
        
        self.proxy_type_combo = QComboBox()
        self.proxy_type_combo.addItems(["HTTP", "SOCKS5"])
        proxy_layout.addRow("Proxy Type:", self.proxy_type_combo)
        
        self.proxy_host_edit = QLineEdit()
        proxy_layout.addRow("Proxy Host:", self.proxy_host_edit)
        
        self.proxy_port_spin = QSpinBox()
        self.proxy_port_spin.setRange(1, 65535)
        proxy_layout.addRow("Proxy Port:", self.proxy_port_spin)
        
        layout.addWidget(proxy_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Network")
    
    def create_appearance_tab(self):
        """Create appearance settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Theme settings
        theme_group = QGroupBox("Theme")
        theme_layout = QFormLayout(theme_group)
        
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["System", "Light", "Dark"])
        theme_layout.addRow("Theme:", self.theme_combo)
        
        self.accent_color_combo = QComboBox()
        self.accent_color_combo.addItems(["Blue", "Green", "Red", "Purple", "Orange"])
        theme_layout.addRow("Accent Color:", self.accent_color_combo)
        
        layout.addWidget(theme_group)
        
        # Font settings
        font_group = QGroupBox("Fonts")
        font_layout = QFormLayout(font_group)
        
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 24)
        font_layout.addRow("Font Size:", self.font_size_spin)
        
        self.font_family_combo = QComboBox()
        self.font_family_combo.addItems(["System Default", "Arial", "Helvetica", "Times New Roman"])
        font_layout.addRow("Font Family:", self.font_family_combo)
        
        layout.addWidget(font_group)
        
        # Interface settings
        interface_group = QGroupBox("Interface")
        interface_layout = QFormLayout(interface_group)
        
        self.show_toolbar_check = QCheckBox("Show toolbar")
        interface_layout.addRow(self.show_toolbar_check)
        
        self.show_statusbar_check = QCheckBox("Show status bar")
        interface_layout.addRow(self.show_statusbar_check)
        
        self.compact_mode_check = QCheckBox("Compact mode")
        interface_layout.addRow(self.compact_mode_check)
        
        layout.addWidget(interface_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Appearance")
    
    def create_advanced_tab(self):
        """Create advanced settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Logging settings
        logging_group = QGroupBox("Logging")
        logging_layout = QFormLayout(logging_group)
        
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        logging_layout.addRow("Log Level:", self.log_level_combo)
        
        self.log_to_file_check = QCheckBox("Log to file")
        logging_layout.addRow(self.log_to_file_check)
        
        layout.addWidget(logging_group)
        
        # Performance settings
        performance_group = QGroupBox("Performance")
        performance_layout = QFormLayout(performance_group)
        
        self.thread_count_spin = QSpinBox()
        self.thread_count_spin.setRange(1, 16)
        performance_layout.addRow("Worker Threads:", self.thread_count_spin)
        
        self.memory_limit_spin = QSpinBox()
        self.memory_limit_spin.setRange(100, 2000)
        self.memory_limit_spin.setSuffix(" MB")
        performance_layout.addRow("Memory Limit:", self.memory_limit_spin)
        
        layout.addWidget(performance_group)
        
        # Debug settings
        debug_group = QGroupBox("Debug")
        debug_layout = QFormLayout(debug_group)
        
        self.debug_mode_check = QCheckBox("Enable debug mode")
        debug_layout.addRow(self.debug_mode_check)
        
        self.verbose_logging_check = QCheckBox("Verbose logging")
        debug_layout.addRow(self.verbose_logging_check)
        
        layout.addWidget(debug_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Advanced")
    
    def load_settings(self):
        """Load current settings"""
        # Simulate loading settings (replace with actual settings loading)
        return {
            'startup_playlist': 'None',
            'minimize_to_tray': False,
            'auto_update': True,
            'data_directory': '/home/<USER>/.iptv_player',
            'cache_size': 500,
            'video_backend': 'VLC',
            'hardware_accel': True,
            'deinterlace': False,
            'audio_device': 'Default',
            'default_volume': 70,
            'buffer_size': 5000,
            'reconnect_attempts': 3,
            'timeout': 30,
            'user_agent': 'IPTV Player 2.0',
            'proxy_enabled': False,
            'proxy_type': 'HTTP',
            'proxy_host': '',
            'proxy_port': 8080,
            'theme': 'System',
            'accent_color': 'Blue',
            'font_size': 10,
            'font_family': 'System Default',
            'show_toolbar': True,
            'show_statusbar': True,
            'compact_mode': False,
            'log_level': 'INFO',
            'log_to_file': True,
            'thread_count': 4,
            'memory_limit': 512,
            'debug_mode': False,
            'verbose_logging': False
        }
    
    def populate_settings(self):
        """Populate UI with current settings"""
        # General tab
        self.startup_playlist_combo.setCurrentText(self.settings['startup_playlist'])
        self.minimize_to_tray_check.setChecked(self.settings['minimize_to_tray'])
        self.auto_update_check.setChecked(self.settings['auto_update'])
        self.data_dir_edit.setText(self.settings['data_directory'])
        self.cache_size_spin.setValue(self.settings['cache_size'])
        
        # Player tab
        self.video_backend_combo.setCurrentText(self.settings['video_backend'])
        self.hardware_accel_check.setChecked(self.settings['hardware_accel'])
        self.deinterlace_check.setChecked(self.settings['deinterlace'])
        self.audio_device_combo.setCurrentText(self.settings['audio_device'])
        self.default_volume_slider.setValue(self.settings['default_volume'])
        self.buffer_size_spin.setValue(self.settings['buffer_size'])
        self.reconnect_attempts_spin.setValue(self.settings['reconnect_attempts'])
        
        # Network tab
        self.timeout_spin.setValue(self.settings['timeout'])
        self.user_agent_edit.setText(self.settings['user_agent'])
        self.proxy_enabled_check.setChecked(self.settings['proxy_enabled'])
        self.proxy_type_combo.setCurrentText(self.settings['proxy_type'])
        self.proxy_host_edit.setText(self.settings['proxy_host'])
        self.proxy_port_spin.setValue(self.settings['proxy_port'])
        
        # Appearance tab
        self.theme_combo.setCurrentText(self.settings['theme'])
        self.accent_color_combo.setCurrentText(self.settings['accent_color'])
        self.font_size_spin.setValue(self.settings['font_size'])
        self.font_family_combo.setCurrentText(self.settings['font_family'])
        self.show_toolbar_check.setChecked(self.settings['show_toolbar'])
        self.show_statusbar_check.setChecked(self.settings['show_statusbar'])
        self.compact_mode_check.setChecked(self.settings['compact_mode'])
        
        # Advanced tab
        self.log_level_combo.setCurrentText(self.settings['log_level'])
        self.log_to_file_check.setChecked(self.settings['log_to_file'])
        self.thread_count_spin.setValue(self.settings['thread_count'])
        self.memory_limit_spin.setValue(self.settings['memory_limit'])
        self.debug_mode_check.setChecked(self.settings['debug_mode'])
        self.verbose_logging_check.setChecked(self.settings['verbose_logging'])
    
    def browse_data_directory(self):
        """Browse for data directory"""
        directory = QFileDialog.getExistingDirectory(
            self,
            "Select Data Directory",
            self.data_dir_edit.text()
        )
        
        if directory:
            self.data_dir_edit.setText(directory)
    
    def collect_settings(self):
        """Collect settings from UI"""
        return {
            'startup_playlist': self.startup_playlist_combo.currentText(),
            'minimize_to_tray': self.minimize_to_tray_check.isChecked(),
            'auto_update': self.auto_update_check.isChecked(),
            'data_directory': self.data_dir_edit.text(),
            'cache_size': self.cache_size_spin.value(),
            'video_backend': self.video_backend_combo.currentText(),
            'hardware_accel': self.hardware_accel_check.isChecked(),
            'deinterlace': self.deinterlace_check.isChecked(),
            'audio_device': self.audio_device_combo.currentText(),
            'default_volume': self.default_volume_slider.value(),
            'buffer_size': self.buffer_size_spin.value(),
            'reconnect_attempts': self.reconnect_attempts_spin.value(),
            'timeout': self.timeout_spin.value(),
            'user_agent': self.user_agent_edit.text(),
            'proxy_enabled': self.proxy_enabled_check.isChecked(),
            'proxy_type': self.proxy_type_combo.currentText(),
            'proxy_host': self.proxy_host_edit.text(),
            'proxy_port': self.proxy_port_spin.value(),
            'theme': self.theme_combo.currentText(),
            'accent_color': self.accent_color_combo.currentText(),
            'font_size': self.font_size_spin.value(),
            'font_family': self.font_family_combo.currentText(),
            'show_toolbar': self.show_toolbar_check.isChecked(),
            'show_statusbar': self.show_statusbar_check.isChecked(),
            'compact_mode': self.compact_mode_check.isChecked(),
            'log_level': self.log_level_combo.currentText(),
            'log_to_file': self.log_to_file_check.isChecked(),
            'thread_count': self.thread_count_spin.value(),
            'memory_limit': self.memory_limit_spin.value(),
            'debug_mode': self.debug_mode_check.isChecked(),
            'verbose_logging': self.verbose_logging_check.isChecked()
        }
    
    def apply_settings(self):
        """Apply settings without closing dialog"""
        settings = self.collect_settings()
        self.save_settings(settings)
        self.logger.info("Settings applied")
    
    def accept_settings(self):
        """Accept and save settings"""
        settings = self.collect_settings()
        self.save_settings(settings)
        self.accept()
    
    def save_settings(self, settings):
        """Save settings to storage"""
        # Simulate saving settings (replace with actual settings saving)
        self.settings = settings
        self.logger.info("Settings saved")
    
    def restore_defaults(self):
        """Restore default settings"""
        reply = QMessageBox.question(
            self,
            "Restore Defaults",
            "Are you sure you want to restore all settings to their default values?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.settings = self.load_default_settings()
            self.populate_settings()
            self.logger.info("Settings restored to defaults")
    
    def load_default_settings(self):
        """Load default settings"""
        return self.load_settings()  # For now, use the same as load_settings
