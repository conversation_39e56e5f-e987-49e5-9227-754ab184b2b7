"""
Playlist Manager Widget for IPTV Player (PyQt6)
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QListWidget, 
    QListWidgetItem, QPushButton, QGroupBox, QLabel,
    QDialog, QDialogButtonBox, QLineEdit, QComboBox,
    QTextEdit, QFormLayout, QFileDialog, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon

from ..core.logger import LoggerMixin


class PlaylistManagerWidget(QWidget, LoggerMixin):
    """Widget for managing IPTV playlists"""
    
    # Signals
    playlist_selected = pyqtSignal(dict)  # Playlist data
    playlist_added = pyqtSignal(str)      # Playlist path/URL
    playlist_removed = pyqtSignal(str)    # Playlist ID
    
    def __init__(self):
        super().__init__()
        
        self.playlists = []
        
        self.init_ui()
        self.load_playlists()
        
        self.logger.info("Playlist manager widget initialized")
    
    def init_ui(self):
        """Initialize the UI"""
        layout = QVBoxLayout(self)
        
        # Playlist group with dark theme
        playlist_group = QGroupBox("Playlists")
        playlist_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #555;
                border-radius: 5px;
                margin-top: 10px;
                background-color: #2b2b2b;
                color: #ffffff;
                font-size: 12px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #ffffff;
            }
        """)
        playlist_layout = QVBoxLayout(playlist_group)
        
        # Playlist list with dark theme
        self.playlist_list = QListWidget()
        self.playlist_list.itemSelectionChanged.connect(self.on_selection_changed)
        self.playlist_list.itemDoubleClicked.connect(self.on_playlist_double_clicked)
        self.playlist_list.setStyleSheet("""
            QListWidget {
                font-size: 11px;
                padding: 2px;
                border: 1px solid #555;
                border-radius: 5px;
                background-color: #2b2b2b;
                color: #ffffff;
                min-height: 200px;
            }
            QListWidget::item {
                padding: 5px;
                margin: 1px;
                border-radius: 3px;
                border: 1px solid transparent;
                background-color: #3c3c3c;
                color: #ffffff;
                min-height: 35px;
            }
            QListWidget::item:selected {
                background-color: #0078d4;
                color: white;
                border: 1px solid #005a9e;
            }
            QListWidget::item:hover {
                background-color: #4a4a4a;
                border: 1px solid #666;
            }
        """)
        playlist_layout.addWidget(self.playlist_list)
        
        # Playlist count with dark theme
        self.count_label = QLabel("0 playlists")
        self.count_label.setStyleSheet("""
            QLabel {
                color: #cccccc;
                font-size: 10px;
                padding: 2px;
                background-color: #2b2b2b;
            }
        """)
        playlist_layout.addWidget(self.count_label)
        
        layout.addWidget(playlist_group)
        
        # Control buttons
        button_layout = QVBoxLayout()
        
        # Style for dark theme buttons
        button_style = """
            QPushButton {
                background-color: #3c3c3c;
                border: 1px solid #555;
                border-radius: 4px;
                padding: 6px 12px;
                color: #ffffff;
                font-size: 11px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4a4a4a;
                border: 1px solid #666;
            }
            QPushButton:pressed {
                background-color: #2a2a2a;
                border: 1px solid #0078d4;
            }
            QPushButton:disabled {
                background-color: #2a2a2a;
                border: 1px solid #444;
                color: #666;
            }
        """

        self.add_button = QPushButton("➕ Add")
        self.add_button.clicked.connect(self.show_add_dialog)
        self.add_button.setStyleSheet(button_style)
        button_layout.addWidget(self.add_button)

        self.edit_button = QPushButton("✏️ Edit")
        self.edit_button.setEnabled(False)
        self.edit_button.clicked.connect(self.edit_playlist)
        self.edit_button.setStyleSheet(button_style)
        button_layout.addWidget(self.edit_button)

        self.remove_button = QPushButton("🗑️ Remove")
        self.remove_button.setEnabled(False)
        self.remove_button.clicked.connect(self.remove_playlist)
        self.remove_button.setStyleSheet(button_style)
        button_layout.addWidget(self.remove_button)
        
        self.refresh_button = QPushButton("Refresh")
        self.refresh_button.clicked.connect(self.refresh_playlists)
        button_layout.addWidget(self.refresh_button)
        
        layout.addLayout(button_layout)
    
    def load_playlists(self):
        """Load playlists from database/storage"""
        try:
            # Try to load from file
            import json
            import os
            from pathlib import Path

            # Create data directory if it doesn't exist
            data_dir = Path.home() / '.iptv_player'
            data_dir.mkdir(exist_ok=True)

            playlists_file = data_dir / 'playlists.json'

            if playlists_file.exists():
                with open(playlists_file, 'r', encoding='utf-8') as f:
                    self.playlists = json.load(f)
                self.logger.info(f"Loaded {len(self.playlists)} playlists from file")
            else:
                # Initialize with empty list
                self.playlists = []
                self.logger.info("No saved playlists found, starting with empty list")

        except Exception as e:
            self.logger.error(f"Failed to load playlists: {e}")
            # Fallback to empty list
            self.playlists = []

        self.update_playlist_list()
        self.logger.info(f"Displaying {len(self.playlists)} playlists")

    def save_playlists(self):
        """Save playlists to storage"""
        try:
            import json
            from pathlib import Path

            # Create data directory if it doesn't exist
            data_dir = Path.home() / '.iptv_player'
            data_dir.mkdir(exist_ok=True)

            playlists_file = data_dir / 'playlists.json'

            with open(playlists_file, 'w', encoding='utf-8') as f:
                json.dump(self.playlists, f, indent=2, ensure_ascii=False)

            self.logger.info(f"Saved {len(self.playlists)} playlists to file")

        except Exception as e:
            self.logger.error(f"Failed to save playlists: {e}")
    
    def update_playlist_list(self):
        """Update the playlist list display"""
        self.playlist_list.clear()
        
        for playlist in self.playlists:
            item = QListWidgetItem()

            # Create compact display text
            name = playlist['name']
            playlist_type = playlist['type']

            # Shorten name if too long
            if len(name) > 15:
                name = name[:12] + "..."

            # Create compact display
            display_text = f"📋 {name}"
            display_text += f"\n   {playlist_type}"

            # Add channel count if available
            if playlist.get('channel_count'):
                count = playlist['channel_count']
                display_text += f" ({count})"

            item.setText(display_text)
            item.setData(Qt.ItemDataRole.UserRole, playlist)

            # Set tooltip with full info
            tooltip = f"Name: {playlist['name']}\nType: {playlist_type}"
            if playlist.get('url'):
                tooltip += f"\nURL: {playlist['url']}"
            if playlist.get('channel_count'):
                tooltip += f"\nChannels: {playlist['channel_count']}"
            if playlist.get('last_updated'):
                tooltip += f"\nUpdated: {playlist['last_updated']}"
            item.setToolTip(tooltip)

            # Add to list
            self.playlist_list.addItem(item)
        
        # Update count
        self.count_label.setText(f"{len(self.playlists)} playlists")
    
    def on_selection_changed(self):
        """Handle selection change"""
        has_selection = bool(self.playlist_list.currentItem())
        self.edit_button.setEnabled(has_selection)
        self.remove_button.setEnabled(has_selection)
    
    def on_playlist_double_clicked(self, item):
        """Handle playlist double-click"""
        playlist_data = item.data(Qt.ItemDataRole.UserRole)
        if playlist_data:
            self.playlist_selected.emit(playlist_data)
    
    def show_add_dialog(self):
        """Show add playlist dialog"""
        dialog = AddPlaylistDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            playlist_data = dialog.get_playlist_data()
            self.add_playlist(playlist_data)
    
    def add_playlist(self, playlist_data):
        """Add a new playlist"""
        try:
            # Generate ID
            import uuid
            from datetime import datetime

            playlist_data['id'] = str(uuid.uuid4())
            playlist_data['channel_count'] = 0
            playlist_data['last_updated'] = datetime.now().isoformat()
            playlist_data['created_at'] = datetime.now().isoformat()

            # Add to list
            self.playlists.append(playlist_data)

            # Save to file
            self.save_playlists()

            # Update display
            self.update_playlist_list()

            # Emit signal
            self.playlist_added.emit(playlist_data.get('url', ''))

            self.logger.info(f"Added playlist: {playlist_data['name']}")

            # Show success message
            QMessageBox.information(
                self,
                "Success",
                f"Playlist '{playlist_data['name']}' added successfully!"
            )

        except Exception as e:
            self.logger.error(f"Failed to add playlist: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to add playlist: {str(e)}"
            )
    
    def edit_playlist(self):
        """Edit selected playlist"""
        current_item = self.playlist_list.currentItem()
        if not current_item:
            return
        
        playlist_data = current_item.data(Qt.ItemDataRole.UserRole)
        
        dialog = AddPlaylistDialog(self, playlist_data)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            updated_data = dialog.get_playlist_data()
            
            # Update playlist data
            for i, playlist in enumerate(self.playlists):
                if playlist['id'] == playlist_data['id']:
                    self.playlists[i].update(updated_data)
                    break

            # Save to file
            self.save_playlists()

            # Update display
            self.update_playlist_list()

            self.logger.info(f"Updated playlist: {updated_data['name']}")
    
    def remove_playlist(self):
        """Remove selected playlist"""
        current_item = self.playlist_list.currentItem()
        if not current_item:
            return
        
        playlist_data = current_item.data(Qt.ItemDataRole.UserRole)
        
        # Confirm removal
        reply = QMessageBox.question(
            self,
            "Remove Playlist",
            f"Are you sure you want to remove '{playlist_data['name']}'?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # Remove from list
            self.playlists = [p for p in self.playlists if p['id'] != playlist_data['id']]

            # Save to file
            self.save_playlists()

            # Update display
            self.update_playlist_list()

            # Emit signal
            self.playlist_removed.emit(playlist_data['id'])

            self.logger.info(f"Removed playlist: {playlist_data['name']}")
    
    def refresh_playlists(self):
        """Refresh playlist list"""
        self.load_playlists()
        self.logger.info("Playlists refreshed")
    
    def get_selected_playlist(self):
        """Get currently selected playlist data"""
        current_item = self.playlist_list.currentItem()
        if current_item:
            return current_item.data(Qt.ItemDataRole.UserRole)
        return None


class AddPlaylistDialog(QDialog):
    """Dialog for adding/editing playlists"""
    
    def __init__(self, parent=None, playlist_data=None):
        super().__init__(parent)
        
        self.playlist_data = playlist_data
        self.is_editing = playlist_data is not None
        
        self.setWindowTitle("Edit Playlist" if self.is_editing else "Add Playlist")
        self.setModal(True)
        self.resize(400, 300)
        
        self.init_ui()
        
        if self.is_editing:
            self.populate_fields()
    
    def init_ui(self):
        """Initialize the dialog UI"""
        layout = QVBoxLayout(self)
        
        # Form layout
        form_layout = QFormLayout()
        
        # Playlist name
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("Enter playlist name")
        form_layout.addRow("Name:", self.name_edit)
        
        # Playlist type
        self.type_combo = QComboBox()
        self.type_combo.addItems(["M3U", "M3U8", "XTREAM", "STALKER"])
        self.type_combo.currentTextChanged.connect(self.on_type_changed)
        form_layout.addRow("Type:", self.type_combo)
        
        # URL/File
        url_layout = QHBoxLayout()
        self.url_edit = QLineEdit()
        self.url_edit.setPlaceholderText("Enter URL or select file")
        url_layout.addWidget(self.url_edit)

        self.browse_button = QPushButton("Browse")
        self.browse_button.clicked.connect(self.browse_file)
        url_layout.addWidget(self.browse_button)

        # Store reference to the URL label for dynamic updates
        self.url_label = QLabel("URL/File:")
        form_layout.addRow(self.url_label, url_layout)
        
        # MAC Address (for Stalker)
        self.mac_edit = QLineEdit()
        self.mac_edit.setPlaceholderText("00:1A:79:XX:XX:XX")
        self.mac_row = form_layout.addRow("MAC Address:", self.mac_edit)

        # Username (for Xtream/Stalker - optional for Stalker)
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("Username")
        self.username_row = form_layout.addRow("Username:", self.username_edit)

        # Password (for Xtream/Stalker - optional for Stalker)
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_edit.setPlaceholderText("Password")
        self.password_row = form_layout.addRow("Password:", self.password_edit)
        
        layout.addLayout(form_layout)
        
        # Description
        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText("Optional description")
        self.description_edit.setMaximumHeight(80)
        layout.addWidget(QLabel("Description:"))
        layout.addWidget(self.description_edit)
        
        # Buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        # Initial type change
        self.on_type_changed()
    
    def on_type_changed(self):
        """Handle playlist type change"""
        playlist_type = self.type_combo.currentText()

        # Show/hide fields based on type
        if playlist_type == "STALKER":
            # Stalker: Show MAC (required), Username/Password (optional)
            self.mac_edit.setVisible(True)
            self.username_edit.setVisible(True)
            self.password_edit.setVisible(True)

            # Update labels and placeholders for Stalker
            self.url_label.setText("Portal URL:")
            self.url_edit.setPlaceholderText("http://portal.example.com")
            self.username_edit.setPlaceholderText("Username (optional)")
            self.password_edit.setPlaceholderText("Password (optional)")

        elif playlist_type == "XTREAM":
            # Xtream: Hide MAC, Show Username/Password (required)
            self.mac_edit.setVisible(False)
            self.username_edit.setVisible(True)
            self.password_edit.setVisible(True)

            # Update labels and placeholders for Xtream
            self.url_label.setText("Server URL:")
            self.url_edit.setPlaceholderText("http://server.com:8080")
            self.username_edit.setPlaceholderText("Username (required)")
            self.password_edit.setPlaceholderText("Password (required)")

        else:
            # M3U/M3U8: Hide all auth fields
            self.mac_edit.setVisible(False)
            self.username_edit.setVisible(False)
            self.password_edit.setVisible(False)

            # Update labels and placeholders for M3U
            self.url_label.setText("URL/File:")
            self.url_edit.setPlaceholderText("Enter URL or select file")
    
    def browse_file(self):
        """Browse for playlist file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Playlist File",
            "",
            "Playlist Files (*.m3u *.m3u8);;All Files (*)"
        )
        
        if file_path:
            self.url_edit.setText(file_path)
    
    def populate_fields(self):
        """Populate fields with existing playlist data"""
        if not self.playlist_data:
            return
        
        self.name_edit.setText(self.playlist_data.get('name', ''))
        
        playlist_type = self.playlist_data.get('type', 'M3U')
        index = self.type_combo.findText(playlist_type)
        if index >= 0:
            self.type_combo.setCurrentIndex(index)
        
        self.url_edit.setText(self.playlist_data.get('url', ''))
        self.username_edit.setText(self.playlist_data.get('username', ''))
        self.password_edit.setText(self.playlist_data.get('password', ''))
        self.description_edit.setPlainText(self.playlist_data.get('description', ''))
    
    def get_playlist_data(self):
        """Get playlist data from form"""
        data = {
            'name': self.name_edit.text().strip(),
            'type': self.type_combo.currentText(),
            'url': self.url_edit.text().strip(),
            'description': self.description_edit.toPlainText().strip()
        }
        
        # Add authentication fields if needed
        if data['type'] in ["XTREAM", "STALKER"]:
            data['username'] = self.username_edit.text().strip()
            data['password'] = self.password_edit.text().strip()
        
        return data
    
    def accept(self):
        """Validate and accept dialog"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "Validation Error", "Please enter a playlist name.")
            return
        
        if not self.url_edit.text().strip():
            QMessageBox.warning(self, "Validation Error", "Please enter a URL or select a file.")
            return
        
        playlist_type = self.type_combo.currentText()
        if playlist_type in ["XTREAM", "STALKER"]:
            if not self.username_edit.text().strip() or not self.password_edit.text().strip():
                QMessageBox.warning(self, "Validation Error", "Please enter username and password.")
                return
        
        super().accept()
