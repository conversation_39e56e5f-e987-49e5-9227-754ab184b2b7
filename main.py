#!/usr/bin/env python3
"""
IPTV Player - Main Application Entry Point (PyQt6 Version)
A modern IPTV player with support for M3U/M3U8, Xtream Codes, and Stalker Portal
"""

import sys
import os
import asyncio
from pathlib import Path

# Add src directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

try:
    # Import PyQt6
    from PyQt6.QtWidgets import QApplication, QMainWindow, QMessageBox
    from PyQt6.QtCore import Qt, QTimer
    from PyQt6.QtGui import QIcon
    
    # Import application components
    from src.core.app_config import AppConfig
    from src.core.logger import setup_logger
    from src.core.database import DatabaseManager
    from src.core.cache_manager import CacheManager
    from src.core.security import SecurityManager
    
    # Import UI components
    from src.ui.main_window import MainWindow
    
    class IPTVPlayerApp(QApplication):
        """Main IPTV Player Application"""
        
        def __init__(self, argv):
            super().__init__(argv)
            
            # Set application properties
            self.setApplicationName("IPTV Player")
            self.setApplicationVersion("2.0.0")
            self.setOrganizationName("IPTV Player")
            
            # Initialize core components
            self.config = None
            self.db_manager = None
            self.cache_manager = None
            self.security_manager = None
            self.logger = None
            self.main_window = None
            
            # Initialize application
            self.init_application()
            
        def init_application(self):
            """Initialize the application"""
            try:
                # Initialize logging
                self.logger = setup_logger(__name__)
                self.logger.info("Starting IPTV Player application (PyQt6)")
                
                # Load configuration
                self.config = AppConfig()
                
                # Initialize database
                self.db_manager = DatabaseManager()
                self.db_manager.initialize_database()
                self.logger.info("Database initialized successfully")
                
                # Initialize cache manager
                self.cache_manager = CacheManager()
                
                # Initialize security manager
                self.security_manager = SecurityManager()
                
                # Create main window
                self.main_window = MainWindow(self)
                self.main_window.show()
                
                # Schedule post-initialization
                QTimer.singleShot(100, self.post_init)
                
                self.logger.info("Application initialized successfully")
                
            except Exception as e:
                error_msg = f"Failed to initialize application: {e}"
                if hasattr(self, 'logger') and self.logger:
                    self.logger.error(error_msg)
                else:
                    print(error_msg)
                
                # Show error dialog
                msg_box = QMessageBox()
                msg_box.setIcon(QMessageBox.Icon.Critical)
                msg_box.setWindowTitle("IPTV Player - Initialization Error")
                msg_box.setText("Failed to start application")
                msg_box.setDetailedText(str(e))
                msg_box.setInformativeText("For testing without full dependencies, run: python test_app.py")
                msg_box.exec()
                
                sys.exit(1)
        
        def post_init(self):
            """Post-initialization tasks"""
            try:
                self.logger.info("Post-initialization completed")
                # Additional initialization can be added here
            except Exception as e:
                self.logger.error(f"Post-initialization failed: {e}")
        
        def cleanup(self):
            """Cleanup resources"""
            try:
                if self.logger:
                    self.logger.info("Shutting down IPTV Player application")
                
                # Cleanup resources
                if hasattr(self, 'cache_manager') and self.cache_manager:
                    self.cache_manager.cleanup()
                
                if hasattr(self, 'db_manager') and self.db_manager:
                    self.db_manager.close()
                    
            except Exception as e:
                if self.logger:
                    self.logger.error(f"Error during cleanup: {e}")
                else:
                    print(f"Error during cleanup: {e}")

    def main():
        """Main entry point"""
        try:
            # Create application
            app = IPTVPlayerApp(sys.argv)
            
            # Set up cleanup on exit
            app.aboutToQuit.connect(app.cleanup)
            
            # Run application
            return app.exec()
            
        except Exception as e:
            print(f"Failed to start application: {e}")
            print("\nMissing dependencies: {e}")
            print("Please install required packages:")
            print("pip install -r requirements.txt")
            print("\nTo test the core functionality without UI dependencies, run:")
            print("python test_app.py")
            return 1

    if __name__ == '__main__':
        sys.exit(main())

except ImportError as e:
    print(f"Missing dependencies: {e}")
    print("Please install required packages:")
    print("pip install -r requirements.txt")
    print("\nTo test the core functionality without UI dependencies, run:")
    print("python test_app.py")
    sys.exit(1)
