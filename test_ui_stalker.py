#!/usr/bin/env python3
"""
Test script to verify Stalker UI changes
"""

import sys
import os

# Add src to path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

try:
    from PyQt6.QtWidgets import QApplication
    from src.ui.playlist_manager_widget import AddPlaylistDialog
    
    def test_stalker_ui():
        """Test Stalker UI without username/password"""
        print("🧪 Testing Stalker UI Changes")
        print("=" * 40)
        
        app = QApplication(sys.argv)
        
        # Create dialog
        dialog = AddPlaylistDialog()
        
        # Test initial state (should be M3U - all fields hidden)
        print(f"Initial type: {dialog.type_combo.currentText()}")

        # Set to Stalker type
        stalker_index = dialog.type_combo.findText("STALKER")
        if stalker_index >= 0:
            print(f"Setting combo to STALKER (index {stalker_index})")
            dialog.type_combo.setCurrentIndex(stalker_index)
            print(f"Combo text after setting: {dialog.type_combo.currentText()}")
            dialog.on_type_changed()
            print("Called on_type_changed()")

            # Check visibility after changing to Stalker
            mac_visible = dialog.mac_edit.isVisible()
            mac_label_visible = dialog.mac_label.isVisible()
            username_visible = dialog.username_edit.isVisible()
            username_label_visible = dialog.username_label.isVisible()
            password_visible = dialog.password_edit.isVisible()
            password_label_visible = dialog.password_label.isVisible()

            print(f"✅ Dialog created successfully")
            print(f"   - Current type: {dialog.type_combo.currentText()}")
            print(f"   - MAC Address field visible: {mac_visible}")
            print(f"   - MAC Address label visible: {mac_label_visible}")
            print(f"   - Username field visible: {username_visible}")
            print(f"   - Username label visible: {username_label_visible}")
            print(f"   - Password field visible: {password_visible}")
            print(f"   - Password label visible: {password_label_visible}")

            if mac_visible and mac_label_visible and not username_visible and not username_label_visible and not password_visible and not password_label_visible:
                print("🎉 SUCCESS: Stalker UI shows only MAC address!")
                return True
            else:
                print("❌ FAILED: UI visibility is not correct for Stalker")
                return False
        else:
            print("❌ FAILED: Could not find STALKER option")
            return False
            
    if __name__ == '__main__':
        result = test_stalker_ui()
        sys.exit(0 if result else 1)
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("This test requires PyQt6. The main app should work correctly.")
    sys.exit(0)
