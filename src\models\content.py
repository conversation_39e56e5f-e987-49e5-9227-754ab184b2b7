"""
Content data models for VOD and Series
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from enum import Enum
import json


class ContentType(Enum):
    """Types of content"""
    LIVE_TV = "live_tv"
    VOD = "vod"
    SERIES = "series"
    EPISODE = "episode"


class ContentRating(Enum):
    """Content rating classifications"""
    G = "G"
    PG = "PG"
    PG13 = "PG-13"
    R = "R"
    NC17 = "NC-17"
    TV_Y = "TV-Y"
    TV_Y7 = "TV-Y7"
    TV_G = "TV-G"
    TV_PG = "TV-PG"
    TV_14 = "TV-14"
    TV_MA = "TV-MA"
    UNRATED = "Unrated"


@dataclass
class ContentInfo:
    """Base content information"""
    
    id: str
    title: str
    content_type: ContentType
    url: str
    description: Optional[str] = None
    poster: Optional[str] = None
    backdrop: Optional[str] = None
    genre: Optional[str] = None
    year: Optional[int] = None
    rating: Optional[ContentRating] = None
    duration: Optional[int] = None  # in seconds
    language: Optional[str] = None
    country: Optional[str] = None
    director: Optional[str] = None
    cast: List[str] = field(default_factory=list)
    is_favorite: bool = False
    is_watched: bool = False
    watch_progress: float = 0.0  # percentage watched
    last_watched: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert content to dictionary"""
        return {
            'id': self.id,
            'title': self.title,
            'content_type': self.content_type.value,
            'url': self.url,
            'description': self.description,
            'poster': self.poster,
            'backdrop': self.backdrop,
            'genre': self.genre,
            'year': self.year,
            'rating': self.rating.value if self.rating else None,
            'duration': self.duration,
            'language': self.language,
            'country': self.country,
            'director': self.director,
            'cast': json.dumps(self.cast),
            'is_favorite': self.is_favorite,
            'is_watched': self.is_watched,
            'watch_progress': self.watch_progress,
            'last_watched': self.last_watched.isoformat() if self.last_watched else None,
            'metadata': json.dumps(self.metadata),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ContentInfo':
        """Create content from dictionary"""
        cast = data.get('cast', '[]')
        if isinstance(cast, str):
            cast = json.loads(cast)
        
        metadata = data.get('metadata', '{}')
        if isinstance(metadata, str):
            metadata = json.loads(metadata)
        
        rating = None
        if data.get('rating'):
            rating = ContentRating(data['rating'])
        
        return cls(
            id=data['id'],
            title=data['title'],
            content_type=ContentType(data['content_type']),
            url=data['url'],
            description=data.get('description'),
            poster=data.get('poster'),
            backdrop=data.get('backdrop'),
            genre=data.get('genre'),
            year=data.get('year'),
            rating=rating,
            duration=data.get('duration'),
            language=data.get('language'),
            country=data.get('country'),
            director=data.get('director'),
            cast=cast,
            is_favorite=data.get('is_favorite', False),
            is_watched=data.get('is_watched', False),
            watch_progress=data.get('watch_progress', 0.0),
            last_watched=datetime.fromisoformat(data['last_watched']) if data.get('last_watched') else None,
            metadata=metadata,
            created_at=datetime.fromisoformat(data.get('created_at', datetime.now().isoformat())),
            updated_at=datetime.fromisoformat(data.get('updated_at', datetime.now().isoformat()))
        )
    
    def update_watch_progress(self, progress: float, position_seconds: int = 0) -> None:
        """Update watch progress"""
        self.watch_progress = max(0.0, min(100.0, progress))
        self.last_watched = datetime.now()
        self.updated_at = datetime.now()
        
        if progress >= 90.0:  # Consider watched if 90% or more
            self.is_watched = True
        
        self.metadata['last_position'] = position_seconds
    
    def get_formatted_duration(self) -> str:
        """Get formatted duration string"""
        if not self.duration:
            return "Unknown"
        
        duration = timedelta(seconds=self.duration)
        hours = duration.seconds // 3600
        minutes = (duration.seconds % 3600) // 60
        
        if hours > 0:
            return f"{hours}h {minutes}m"
        else:
            return f"{minutes}m"


@dataclass
class Series(ContentInfo):
    """Represents a TV series"""
    
    seasons: List['Season'] = field(default_factory=list)
    total_seasons: int = 0
    total_episodes: int = 0
    
    def __post_init__(self):
        self.content_type = ContentType.SERIES
    
    def add_season(self, season: 'Season') -> None:
        """Add season to series"""
        if season not in self.seasons:
            self.seasons.append(season)
            season.series_id = self.id
            self.total_seasons = len(self.seasons)
            self.total_episodes = sum(len(s.episodes) for s in self.seasons)
    
    def get_season(self, season_number: int) -> Optional['Season']:
        """Get season by number"""
        for season in self.seasons:
            if season.season_number == season_number:
                return season
        return None


@dataclass
class Season:
    """Represents a season of a series"""
    
    id: str
    series_id: str
    season_number: int
    title: Optional[str] = None
    poster: Optional[str] = None
    episodes: List['Episode'] = field(default_factory=list)
    year: Optional[int] = None
    
    def add_episode(self, episode: 'Episode') -> None:
        """Add episode to season"""
        if episode not in self.episodes:
            self.episodes.append(episode)
            episode.season_id = self.id
            episode.series_id = self.series_id
    
    def get_episode(self, episode_number: int) -> Optional['Episode']:
        """Get episode by number"""
        for episode in self.episodes:
            if episode.episode_number == episode_number:
                return episode
        return None


@dataclass
class Episode(ContentInfo):
    """Represents an episode of a series"""

    def __init__(self, series_id: str, season_id: str, season_number: int,
                 episode_number: int, air_date: Optional[datetime] = None, **kwargs):
        super().__init__(**kwargs)
        self.series_id = series_id
        self.season_id = season_id
        self.season_number = season_number
        self.episode_number = episode_number
        self.air_date = air_date
        self.content_type = ContentType.EPISODE
