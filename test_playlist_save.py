#!/usr/bin/env python3
"""
Test script to verify playlist saving functionality
"""

import json
import os
from pathlib import Path

def test_playlist_save():
    """Test if playlists are being saved correctly"""
    
    # Check if data directory exists
    data_dir = Path.home() / '.iptv_player'
    playlists_file = data_dir / 'playlists.json'
    
    print("🔍 Testing playlist save functionality...")
    print(f"Data directory: {data_dir}")
    print(f"Playlists file: {playlists_file}")
    
    if data_dir.exists():
        print("✅ Data directory exists")
    else:
        print("❌ Data directory does not exist")
        return
    
    if playlists_file.exists():
        print("✅ Playlists file exists")
        
        try:
            with open(playlists_file, 'r', encoding='utf-8') as f:
                playlists = json.load(f)
            
            print(f"📊 Found {len(playlists)} saved playlists:")
            
            for i, playlist in enumerate(playlists, 1):
                print(f"  {i}. {playlist.get('name', 'Unknown')} ({playlist.get('type', 'Unknown')})")
                print(f"     URL: {playlist.get('url', 'No URL')}")
                print(f"     ID: {playlist.get('id', 'No ID')}")
                print()
                
        except Exception as e:
            print(f"❌ Error reading playlists file: {e}")
    else:
        print("ℹ️  No playlists file found (this is normal for first run)")
    
    print("\n📋 Instructions:")
    print("1. Add a playlist in the IPTV Player application")
    print("2. Run this script again to verify it was saved")
    print("3. Restart the application to verify it loads saved playlists")

if __name__ == '__main__':
    test_playlist_save()
