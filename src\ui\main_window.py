"""
Main Window for IPTV Player (PyQt6)
"""

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QMenuBar, QStatusBar, QToolBar,
    QLabel, QPushButton, QSplitter,
    QListWidget, QTextEdit, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QIcon, QKeySequence, QAction

from ..core.logger import LoggerMixin
from .channel_list_widget import ChannelListWidget
from .video_player_widget import VideoPlayerWidget
from .playlist_manager_widget import PlaylistManagerWidget
from .settings_dialog import SettingsDialog


class MainWindow(QMainWindow, LoggerMixin):
    """Main application window"""
    
    # Signals
    channel_selected = pyqtSignal(dict)  # Channel data
    playlist_added = pyqtSignal(str)     # Playlist path/URL
    
    def __init__(self, app):
        super().__init__()
        self.app = app
        
        # Window properties
        self.setWindowTitle("IPTV Player")
        self.setMinimumSize(1200, 800)
        self.resize(1600, 900)
        
        # Initialize UI components
        self.init_ui()
        self.init_menu_bar()
        self.init_toolbar()
        self.init_status_bar()
        
        # Connect signals
        self.connect_signals()
        
        self.logger.info("Main window initialized")
    
    def init_ui(self):
        """Initialize the main UI layout"""
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        
        # Create splitter for resizable panels
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)

        # Left panel - Channel list (expanded)
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)

        # Middle panel - Video player and controls
        middle_panel = self.create_middle_panel()
        splitter.addWidget(middle_panel)

        # Right panel - Playlist manager
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)

        # Set splitter proportions (18% left channels, 62% middle video, 20% right playlist)
        splitter.setSizes([180, 620, 200])
    
    def create_left_panel(self):
        """Create the left panel with expanded channel list"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        # Channel list (expanded to take full left panel)
        self.channel_list = ChannelListWidget()
        left_layout.addWidget(self.channel_list)

        return left_widget

    def create_middle_panel(self):
        """Create the middle panel with video player"""
        middle_widget = QWidget()
        middle_layout = QVBoxLayout(middle_widget)

        # Tab widget for different content types
        self.tab_widget = QTabWidget()
        middle_layout.addWidget(self.tab_widget)

        # Live TV tab
        self.video_player = VideoPlayerWidget()
        self.tab_widget.addTab(self.video_player, "Live TV")

        # VOD tab (placeholder)
        vod_widget = QLabel("VOD Browser\n(Coming Soon)")
        vod_widget.setAlignment(Qt.AlignmentFlag.AlignCenter)
        vod_widget.setStyleSheet("color: gray; font-size: 16px;")
        self.tab_widget.addTab(vod_widget, "Movies")

        # Series tab (placeholder)
        series_widget = QLabel("Series Browser\n(Coming Soon)")
        series_widget.setAlignment(Qt.AlignmentFlag.AlignCenter)
        series_widget.setStyleSheet("color: gray; font-size: 16px;")
        self.tab_widget.addTab(series_widget, "Series")

        return middle_widget

    def create_right_panel(self):
        """Create the right panel with playlist manager"""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)

        # Playlist manager
        self.playlist_manager = PlaylistManagerWidget()
        right_layout.addWidget(self.playlist_manager)

        return right_widget
    
    def init_menu_bar(self):
        """Initialize the menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu('&File')
        
        # Add playlist action
        add_playlist_action = QAction('&Add Playlist...', self)
        add_playlist_action.setShortcut(QKeySequence.StandardKey.Open)
        add_playlist_action.setStatusTip('Add a new IPTV playlist')
        add_playlist_action.triggered.connect(self.show_add_playlist_dialog)
        file_menu.addAction(add_playlist_action)
        
        file_menu.addSeparator()
        
        # Exit action
        exit_action = QAction('E&xit', self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.setStatusTip('Exit application')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # View menu
        view_menu = menubar.addMenu('&View')
        
        # Fullscreen action
        fullscreen_action = QAction('&Fullscreen', self)
        fullscreen_action.setShortcut(QKeySequence.StandardKey.FullScreen)
        fullscreen_action.setCheckable(True)
        fullscreen_action.triggered.connect(self.toggle_fullscreen)
        view_menu.addAction(fullscreen_action)
        
        # Tools menu
        tools_menu = menubar.addMenu('&Tools')
        
        # Settings action
        settings_action = QAction('&Settings...', self)
        settings_action.setShortcut(QKeySequence.StandardKey.Preferences)
        settings_action.setStatusTip('Open application settings')
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)
        
        # Help menu
        help_menu = menubar.addMenu('&Help')
        
        # About action
        about_action = QAction('&About', self)
        about_action.setStatusTip('About IPTV Player')
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def init_toolbar(self):
        """Initialize the toolbar"""
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # Add playlist button
        add_playlist_btn = QPushButton("Add Playlist")
        add_playlist_btn.clicked.connect(self.show_add_playlist_dialog)
        toolbar.addWidget(add_playlist_btn)
        
        toolbar.addSeparator()
        
        # Play/Pause button
        self.play_pause_btn = QPushButton("Play")
        self.play_pause_btn.clicked.connect(self.toggle_playback)
        self.play_pause_btn.setEnabled(False)
        toolbar.addWidget(self.play_pause_btn)
        
        # Stop button
        self.stop_btn = QPushButton("Stop")
        self.stop_btn.clicked.connect(self.stop_playback)
        self.stop_btn.setEnabled(False)
        toolbar.addWidget(self.stop_btn)
    
    def init_status_bar(self):
        """Initialize the status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Status label
        self.status_label = QLabel("Ready")
        self.status_bar.addWidget(self.status_label)
        
        # Connection status
        self.connection_label = QLabel("Disconnected")
        self.status_bar.addPermanentWidget(self.connection_label)
    
    def connect_signals(self):
        """Connect signals between components"""
        # Channel selection
        self.channel_list.channel_selected.connect(self.on_channel_selected)
        
        # Playlist changes
        self.playlist_manager.playlist_selected.connect(self.on_playlist_selected)
        self.playlist_manager.playlist_added.connect(self.on_playlist_added)
    
    def on_channel_selected(self, channel_data):
        """Handle channel selection"""
        self.logger.info(f"Channel selected: {channel_data.get('name', 'Unknown')}")
        
        # Update status
        self.status_label.setText(f"Loading: {channel_data.get('name', 'Unknown')}")
        
        # Play channel
        self.video_player.play_channel(channel_data)
        
        # Update controls
        self.play_pause_btn.setEnabled(True)
        self.stop_btn.setEnabled(True)
        self.play_pause_btn.setText("Pause")
    
    def on_playlist_selected(self, playlist_data):
        """Handle playlist selection"""
        self.logger.info(f"Playlist selected: {playlist_data.get('name', 'Unknown')}")
        
        # Load channels from playlist
        self.channel_list.load_playlist(playlist_data)
        
        # Update status
        self.status_label.setText(f"Loaded playlist: {playlist_data.get('name', 'Unknown')}")
    
    def on_playlist_added(self, playlist_path):
        """Handle new playlist addition"""
        self.logger.info(f"Playlist added: {playlist_path}")
        
        # Refresh playlist manager
        self.playlist_manager.refresh_playlists()
        
        # Update status
        self.status_label.setText("Playlist added successfully")
    
    def show_add_playlist_dialog(self):
        """Show add playlist dialog"""
        self.playlist_manager.show_add_dialog()
    
    def show_settings(self):
        """Show settings dialog"""
        dialog = SettingsDialog(self)
        dialog.exec()
    
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            "About IPTV Player",
            "IPTV Player v2.0.0\n\n"
            "A modern IPTV player with support for:\n"
            "• M3U/M3U8 playlists\n"
            "• Xtream Codes API\n"
            "• Stalker Portal\n\n"
            "Built with PyQt6 and VLC"
        )
    
    def toggle_fullscreen(self):
        """Toggle fullscreen mode"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()
    
    def toggle_playback(self):
        """Toggle play/pause"""
        if self.video_player.is_playing():
            self.video_player.pause()
            self.play_pause_btn.setText("Play")
        else:
            self.video_player.play()
            self.play_pause_btn.setText("Pause")
    
    def stop_playback(self):
        """Stop playback"""
        self.video_player.stop()
        self.play_pause_btn.setText("Play")
        self.play_pause_btn.setEnabled(False)
        self.stop_btn.setEnabled(False)
        self.status_label.setText("Stopped")
    
    def closeEvent(self, event):
        """Handle window close event"""
        self.logger.info("Main window closing")
        
        # Stop video player
        if hasattr(self, 'video_player'):
            self.video_player.cleanup()
        
        event.accept()
