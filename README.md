# IPTV Player (PyQt6)

A modern, cross-platform IPTV player application built with Python and PyQt6.

## Features

- **Multiple Playlist Formats**: Support for M3U, M3U8, Xtream Codes API, and Stalker Portal
- **Modern UI**: Clean, responsive interface built with PyQt6
- **Video Player**: Integrated VLC-based video player with full controls
- **Channel Management**: Organize channels by categories, favorites, and custom groups
- **Security**: PIN protection and parental controls
- **Performance**: Intelligent caching system for optimal performance
- **Cross-Platform**: Works on Windows, macOS, and Linux

## Installation

### Prerequisites

- Python 3.8 or higher
- VLC Media Player (for video playback)

### Quick Start

1. Install dependencies:
```bash
pip install PyQt6==6.7.1 python-vlc
```

2. Run the application:
```bash
python main_pyqt6.py
```

### Alternative Testing

If you encounter dependency issues, you can test the core functionality without the GUI:

```bash
python test_app.py
```

## Supported Formats

### Playlist Types
- **M3U/M3U8**: Standard playlist format with extended metadata
- **Xtream Codes API**: Full API integration with authentication
- **Stalker Portal**: MAC-based authentication support

### Content Types
- Live TV channels
- Video on Demand (VOD) - Coming Soon
- TV Series - Coming Soon
- Radio stations

## Configuration

The application automatically creates configuration directories:

- **Windows**: `%USERPROFILE%\.iptv_player\`
- **macOS/Linux**: `~/.iptv_player/`

### Directory Structure
```
~/.iptv_player/
├── cache/          # Cached images and data
├── logs/           # Application logs
├── playlists/      # Stored playlists
└── config.json     # Application settings
```

## Usage

### Adding Playlists

1. Click the "Add Playlist" button in the toolbar
2. Choose your playlist type:
   - **M3U/M3U8**: Enter URL or select local file
   - **Xtream Codes**: Enter server URL, username, and password
   - **Stalker Portal**: Enter portal URL and MAC address

### Playing Content

1. Select a playlist from the playlist manager
2. Browse channels in the channel list
3. Double-click a channel to start playback
4. Use the video controls for playback management

### Settings

Access settings through the menu bar (Tools → Settings) to configure:

- Player preferences
- Network settings
- Security options
- Cache management
- UI customization

## Architecture

The application follows a modular architecture:

```
src/
├── core/           # Core functionality
├── models/         # Data models
├── parsers/        # Playlist parsers
├── providers/      # Content providers
├── ui/             # User interface (PyQt6)
└── utils/          # Utility functions
```

## Key Components

### UI Components (PyQt6)
- **MainWindow**: Main application window with menu bar and toolbar
- **ChannelListWidget**: Channel browsing with search and filtering
- **VideoPlayerWidget**: VLC-based video player with controls
- **PlaylistManagerWidget**: Playlist management interface
- **SettingsDialog**: Comprehensive settings interface

### Core Components
- **Database**: SQLite-based data storage
- **Cache Manager**: Intelligent caching system
- **Security Manager**: Authentication and parental controls
- **Logger**: Comprehensive logging system

## Development

### Testing

For core functionality testing without GUI dependencies:

```bash
python test_app.py
```

For simple functionality test:

```bash
python simple_test.py
```

## Files Structure

### Essential Files for PyQt6 Version:
- `main_pyqt6.py` - Main application entry point
- `requirements.txt` - Python dependencies
- `src/ui/main_window.py` - Main window
- `src/ui/channel_list_widget.py` - Channel list
- `src/ui/video_player_widget.py` - Video player
- `src/ui/playlist_manager_widget.py` - Playlist manager
- `src/ui/settings_dialog.py` - Settings dialog
- `src/core/` - Core functionality modules

## License

This project is licensed under the MIT License.

## Support

For support and bug reports, please open an issue on the project repository.

## Acknowledgments

- Built with [PyQt6](https://www.riverbankcomputing.com/software/pyqt/)
- Video playback powered by [VLC](https://www.videolan.org/vlc/)
