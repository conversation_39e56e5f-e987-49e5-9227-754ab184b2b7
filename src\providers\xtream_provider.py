"""
Xtream Codes API provider for IPTV Player
"""

import json
import uuid
import asyncio
import aiohttp
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from urllib.parse import urljoin

from ..models.channel import Channel, ChannelGroup
from ..models.content import ContentInfo, ContentType, Series, Season, Episode
from ..models.playlist import PlaylistCredentials, AuthType
from ..core.logger import LoggerMixin


class XtreamCodesProvider(LoggerMixin):
    """Provider for Xtream Codes API"""
    
    def __init__(self, credentials: PlaylistCredentials):
        if credentials.auth_type != AuthType.USERNAME_PASSWORD:
            raise ValueError("Xtream Codes requires username/password authentication")
        
        self.credentials = credentials
        self.base_url = credentials.server_url.rstrip('/')
        self.username = credentials.username
        self.password = credentials.password
        self.session = None
        self.auth_params = {
            'username': self.username,
            'password': self.password
        }
        
        # Cache for API responses
        self._cache = {}
        self._cache_ttl = timedelta(minutes=30)
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={'User-Agent': 'IPTV Player/1.0.0'}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def authenticate(self) -> bool:
        """Test authentication with Xtream server"""
        try:
            url = f"{self.base_url}/player_api.php"
            params = {**self.auth_params, 'action': 'get_account_info'}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'user_info' in data:
                        self.logger.info("Xtream authentication successful")
                        return True
                
                self.logger.error(f"Authentication failed: {response.status}")
                return False
                
        except Exception as e:
            self.logger.error(f"Authentication error: {e}")
            return False
    
    async def get_account_info(self) -> Dict[str, Any]:
        """Get account information"""
        return await self._api_request('get_account_info')
    
    async def get_live_categories(self) -> List[Dict[str, Any]]:
        """Get live TV categories"""
        return await self._api_request('get_live_categories')
    
    async def get_vod_categories(self) -> List[Dict[str, Any]]:
        """Get VOD categories"""
        return await self._api_request('get_vod_categories')
    
    async def get_series_categories(self) -> List[Dict[str, Any]]:
        """Get series categories"""
        return await self._api_request('get_series_categories')
    
    async def get_live_streams(self, category_id: Optional[str] = None) -> List[Channel]:
        """Get live TV channels"""
        action = 'get_live_streams'
        params = {}
        if category_id:
            params['category_id'] = category_id
        
        streams = await self._api_request(action, params)
        channels = []
        
        for stream in streams:
            try:
                channel = self._create_channel_from_stream(stream)
                if channel:
                    channels.append(channel)
            except Exception as e:
                self.logger.error(f"Failed to create channel from stream {stream}: {e}")
        
        return channels
    
    async def get_vod_streams(self, category_id: Optional[str] = None) -> List[ContentInfo]:
        """Get VOD content"""
        action = 'get_vod_streams'
        params = {}
        if category_id:
            params['category_id'] = category_id
        
        streams = await self._api_request(action, params)
        content_list = []
        
        for stream in streams:
            try:
                content = self._create_vod_from_stream(stream)
                if content:
                    content_list.append(content)
            except Exception as e:
                self.logger.error(f"Failed to create VOD from stream {stream}: {e}")
        
        return content_list
    
    async def get_series(self, category_id: Optional[str] = None) -> List[Series]:
        """Get series content"""
        action = 'get_series'
        params = {}
        if category_id:
            params['category_id'] = category_id
        
        series_list = await self._api_request(action, params)
        series_objects = []
        
        for series_data in series_list:
            try:
                series = await self._create_series_from_data(series_data)
                if series:
                    series_objects.append(series)
            except Exception as e:
                self.logger.error(f"Failed to create series from data {series_data}: {e}")
        
        return series_objects
    
    async def get_series_info(self, series_id: str) -> Dict[str, Any]:
        """Get detailed series information"""
        return await self._api_request('get_series_info', {'series_id': series_id})
    
    async def get_vod_info(self, vod_id: str) -> Dict[str, Any]:
        """Get detailed VOD information"""
        return await self._api_request('get_vod_info', {'vod_id': vod_id})
    
    async def get_epg(self, stream_id: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Get EPG data for a stream"""
        return await self._api_request('get_simple_data_table', {
            'stream_id': stream_id,
            'limit': limit
        })
    
    def get_stream_url(self, stream_id: str, stream_type: str = 'live') -> str:
        """Generate stream URL"""
        if stream_type == 'live':
            return f"{self.base_url}/live/{self.username}/{self.password}/{stream_id}.m3u8"
        elif stream_type == 'movie':
            return f"{self.base_url}/movie/{self.username}/{self.password}/{stream_id}.mp4"
        elif stream_type == 'series':
            return f"{self.base_url}/series/{self.username}/{self.password}/{stream_id}.mp4"
        else:
            raise ValueError(f"Unknown stream type: {stream_type}")
    
    async def _api_request(self, action: str, params: Optional[Dict[str, Any]] = None) -> Any:
        """Make API request to Xtream server"""
        cache_key = f"{action}_{json.dumps(params or {}, sort_keys=True)}"
        
        # Check cache
        if cache_key in self._cache:
            cached_data, cached_time = self._cache[cache_key]
            if datetime.now() - cached_time < self._cache_ttl:
                return cached_data
        
        url = f"{self.base_url}/player_api.php"
        request_params = {**self.auth_params, 'action': action}
        if params:
            request_params.update(params)
        
        try:
            async with self.session.get(url, params=request_params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # Cache the response
                    self._cache[cache_key] = (data, datetime.now())
                    
                    return data
                else:
                    self.logger.error(f"API request failed: {response.status}")
                    return []
                    
        except Exception as e:
            self.logger.error(f"API request error for {action}: {e}")
            return []
    
    def _create_channel_from_stream(self, stream: Dict[str, Any]) -> Optional[Channel]:
        """Create Channel object from Xtream stream data"""
        try:
            stream_id = str(stream.get('stream_id', ''))
            if not stream_id:
                return None
            
            channel = Channel(
                id=f"xtream_live_{stream_id}",
                name=stream.get('name', 'Unknown Channel'),
                url=self.get_stream_url(stream_id, 'live'),
                group=stream.get('category_name'),
                logo=stream.get('stream_icon'),
                epg_id=stream.get('epg_channel_id'),
                category=stream.get('category_name')
            )
            
            # Add Xtream-specific metadata
            channel.metadata.update({
                'stream_id': stream_id,
                'stream_type': stream.get('stream_type'),
                'tv_archive': stream.get('tv_archive'),
                'direct_source': stream.get('direct_source'),
                'tv_archive_duration': stream.get('tv_archive_duration')
            })
            
            return channel
            
        except Exception as e:
            self.logger.error(f"Failed to create channel: {e}")
            return None
    
    def _create_vod_from_stream(self, stream: Dict[str, Any]) -> Optional[ContentInfo]:
        """Create ContentInfo object from Xtream VOD stream"""
        try:
            stream_id = str(stream.get('stream_id', ''))
            if not stream_id:
                return None
            
            content = ContentInfo(
                id=f"xtream_vod_{stream_id}",
                title=stream.get('name', 'Unknown Movie'),
                content_type=ContentType.VOD,
                url=self.get_stream_url(stream_id, 'movie'),
                description=stream.get('plot'),
                poster=stream.get('stream_icon'),
                backdrop=stream.get('backdrop_path'),
                genre=stream.get('genre'),
                year=self._parse_year(stream.get('releasedate')),
                duration=self._parse_duration(stream.get('duration')),
                language=stream.get('audio_language'),
                country=stream.get('country'),
                director=stream.get('director'),
                cast=self._parse_cast(stream.get('cast'))
            )
            
            # Add Xtream-specific metadata
            content.metadata.update({
                'stream_id': stream_id,
                'category_id': stream.get('category_id'),
                'container_extension': stream.get('container_extension'),
                'rating': stream.get('rating'),
                'tmdb_id': stream.get('tmdb_id')
            })
            
            return content
            
        except Exception as e:
            self.logger.error(f"Failed to create VOD content: {e}")
            return None
    
    async def _create_series_from_data(self, series_data: Dict[str, Any]) -> Optional[Series]:
        """Create Series object from Xtream series data"""
        try:
            series_id = str(series_data.get('series_id', ''))
            if not series_id:
                return None
            
            # Get detailed series info
            series_info = await self.get_series_info(series_id)
            info = series_info.get('info', {})
            seasons_data = series_info.get('seasons', [])
            episodes_data = series_info.get('episodes', {})
            
            series = Series(
                id=f"xtream_series_{series_id}",
                title=series_data.get('name', 'Unknown Series'),
                content_type=ContentType.SERIES,
                url="",  # Series don't have direct URLs
                description=info.get('plot'),
                poster=info.get('cover'),
                backdrop=info.get('backdrop_path'),
                genre=info.get('genre'),
                year=self._parse_year(info.get('releasedate')),
                language=info.get('audio_language'),
                country=info.get('country'),
                director=info.get('director'),
                cast=self._parse_cast(info.get('cast'))
            )
            
            # Add seasons and episodes
            for season_data in seasons_data:
                season = Season(
                    id=f"xtream_season_{series_id}_{season_data['season_number']}",
                    series_id=series.id,
                    season_number=int(season_data['season_number']),
                    title=season_data.get('name'),
                    poster=season_data.get('cover')
                )
                
                # Add episodes for this season
                season_episodes = episodes_data.get(str(season_data['season_number']), [])
                for episode_data in season_episodes:
                    episode = Episode(
                        id=f"xtream_episode_{episode_data['id']}",
                        series_id=series.id,
                        season_id=season.id,
                        season_number=season.season_number,
                        episode_number=int(episode_data['episode_num']),
                        title=episode_data.get('title', f"Episode {episode_data['episode_num']}"),
                        content_type=ContentType.EPISODE,
                        url=self.get_stream_url(episode_data['id'], 'series'),
                        description=episode_data.get('info'),
                        poster=episode_data.get('movie_image'),
                        duration=self._parse_duration(episode_data.get('duration_secs'))
                    )
                    
                    season.add_episode(episode)
                
                series.add_season(season)
            
            return series
            
        except Exception as e:
            self.logger.error(f"Failed to create series: {e}")
            return None
    
    def _parse_year(self, date_str: Optional[str]) -> Optional[int]:
        """Parse year from date string"""
        if not date_str:
            return None
        try:
            return int(date_str.split('-')[0])
        except (ValueError, IndexError):
            return None
    
    def _parse_duration(self, duration: Any) -> Optional[int]:
        """Parse duration to seconds"""
        if not duration:
            return None
        try:
            if isinstance(duration, str):
                # Handle "HH:MM:SS" format
                if ':' in duration:
                    parts = duration.split(':')
                    if len(parts) == 3:
                        hours, minutes, seconds = map(int, parts)
                        return hours * 3600 + minutes * 60 + seconds
                return int(duration)
            return int(duration)
        except (ValueError, TypeError):
            return None
    
    def _parse_cast(self, cast_str: Optional[str]) -> List[str]:
        """Parse cast string to list"""
        if not cast_str:
            return []
        try:
            return [actor.strip() for actor in cast_str.split(',') if actor.strip()]
        except AttributeError:
            return []
