#!/usr/bin/env python3
"""
Safe version of IPTV Player - Main Application Entry Point
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

try:
    # Import PyQt6
    from PyQt6.QtWidgets import QApplication, QMainWindow, QMessageBox
    from PyQt6.QtCore import Qt, QTimer
    from PyQt6.QtGui import QIcon
    
    # Import application components
    from src.core.app_config import AppConfig
    from src.core.logger import setup_logger
    from src.core.database import DatabaseManager
    from src.core.cache_manager import CacheManager
    from src.core.security import SecurityManager
    
    # Import UI components
    from src.ui.main_window import MainWindow
    
    class SafeIPTVPlayerApp(QApplication):
        """Safe IPTV Player Application"""
        
        def __init__(self, argv):
            super().__init__(argv)
            
            # Set application properties
            self.setApplicationName("IPTV Player (Safe Mode)")
            self.setApplicationVersion("2.0.0")
            self.setOrganizationName("IPTV Player")
            
            # Initialize core components
            self.config = None
            self.db_manager = None
            self.cache_manager = None
            self.security_manager = None
            self.logger = None
            self.main_window = None
            
            # Initialize application
            self.init_application()
            
        def init_application(self):
            """Initialize the application safely"""
            try:
                # Initialize logging
                self.logger = setup_logger(__name__)
                self.logger.info("Starting IPTV Player application (Safe Mode)")
                
                # Load configuration
                self.config = AppConfig()
                self.logger.info("Configuration loaded")
                
                # Initialize database
                self.db_manager = DatabaseManager()
                self.db_manager.initialize_database()
                self.logger.info("Database initialized successfully")
                
                # Initialize cache manager
                self.cache_manager = CacheManager()
                self.logger.info("Cache manager initialized")
                
                # Initialize security manager
                self.security_manager = SecurityManager()
                self.logger.info("Security manager initialized")
                
                # Create main window
                self.main_window = MainWindow(self)
                self.main_window.show()
                self.logger.info("Main window created and shown")
                
                # Schedule post-initialization
                QTimer.singleShot(100, self.post_init)
                
                self.logger.info("Application initialized successfully")
                
            except Exception as e:
                error_msg = f"Failed to initialize application: {e}"
                if hasattr(self, 'logger') and self.logger:
                    self.logger.error(error_msg)
                else:
                    print(error_msg)
                
                # Show error dialog
                try:
                    msg_box = QMessageBox()
                    msg_box.setIcon(QMessageBox.Icon.Critical)
                    msg_box.setWindowTitle("IPTV Player - Initialization Error")
                    msg_box.setText("Failed to start application")
                    msg_box.setDetailedText(str(e))
                    msg_box.exec()
                except:
                    print(f"Critical error: {e}")
                
                sys.exit(1)
        
        def post_init(self):
            """Post-initialization tasks"""
            try:
                self.logger.info("Post-initialization completed")
            except Exception as e:
                self.logger.error(f"Post-initialization failed: {e}")
        
        def cleanup(self):
            """Cleanup resources safely"""
            try:
                if self.logger:
                    self.logger.info("Shutting down IPTV Player application")
                
                # Cleanup resources
                if hasattr(self, 'cache_manager') and self.cache_manager:
                    if hasattr(self.cache_manager, 'cleanup'):
                        self.cache_manager.cleanup()
                
                if hasattr(self, 'db_manager') and self.db_manager:
                    self.db_manager.close()
                    
            except Exception as e:
                if self.logger:
                    self.logger.error(f"Error during cleanup: {e}")
                else:
                    print(f"Error during cleanup: {e}")

    def main():
        """Main entry point"""
        try:
            print("Starting IPTV Player in Safe Mode...")
            
            # Create application
            app = SafeIPTVPlayerApp(sys.argv)
            
            # Set up cleanup on exit
            app.aboutToQuit.connect(app.cleanup)
            
            print("Application created successfully, starting event loop...")
            
            # Run application
            return app.exec()
            
        except Exception as e:
            print(f"Failed to start application: {e}")
            import traceback
            traceback.print_exc()
            return 1

    if __name__ == '__main__':
        sys.exit(main())

except ImportError as e:
    print(f"Missing dependencies: {e}")
    print("Please install required packages:")
    print("pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"Unexpected error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
