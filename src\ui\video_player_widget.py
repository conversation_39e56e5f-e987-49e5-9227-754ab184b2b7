"""
Video Player Widget for IPTV Player (PyQt6)
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QPushButton, QSlider, QFrame, QSizePolicy
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QPalette

from ..core.logger import LoggerMixin

try:
    import vlc
    VLC_AVAILABLE = True
except ImportError:
    VLC_AVAILABLE = False


class VideoPlayerWidget(QWidget, LoggerMixin):
    """Video player widget using VLC"""
    
    # Signals
    playback_started = pyqtSignal()
    playback_paused = pyqtSignal()
    playback_stopped = pyqtSignal()
    position_changed = pyqtSignal(float)  # Position as percentage
    
    def __init__(self):
        super().__init__()
        
        self.vlc_instance = None
        self.media_player = None
        self.current_channel = None
        self.is_fullscreen = False
        
        self.init_ui()
        self.init_vlc()
        
        # Timer for updating position
        self.position_timer = QTimer()
        self.position_timer.timeout.connect(self.update_position)
        
        self.logger.info("Video player widget initialized")
    
    def init_ui(self):
        """Initialize the UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Video frame
        self.video_frame = QFrame()
        self.video_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        self.video_frame.setStyleSheet("background-color: black;")
        self.video_frame.setSizePolicy(
            QSizePolicy.Policy.Expanding, 
            QSizePolicy.Policy.Expanding
        )
        layout.addWidget(self.video_frame)
        
        # No video message
        self.no_video_label = QLabel("No video playing\n\nSelect a channel to start watching")
        self.no_video_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.no_video_label.setStyleSheet(
            "color: white; font-size: 16px; background-color: black;"
        )
        
        # Add label to video frame
        frame_layout = QVBoxLayout(self.video_frame)
        frame_layout.addWidget(self.no_video_label)
        
        # Controls
        controls_layout = self.create_controls()
        layout.addLayout(controls_layout)
    
    def create_controls(self):
        """Create video controls"""
        controls_layout = QVBoxLayout()
        
        # Progress bar
        progress_layout = QHBoxLayout()
        
        self.time_label = QLabel("00:00")
        progress_layout.addWidget(self.time_label)
        
        self.progress_slider = QSlider(Qt.Orientation.Horizontal)
        self.progress_slider.setRange(0, 1000)
        self.progress_slider.sliderPressed.connect(self.on_slider_pressed)
        self.progress_slider.sliderReleased.connect(self.on_slider_released)
        progress_layout.addWidget(self.progress_slider)
        
        self.duration_label = QLabel("00:00")
        progress_layout.addWidget(self.duration_label)
        
        controls_layout.addLayout(progress_layout)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.play_button = QPushButton("Play")
        self.play_button.clicked.connect(self.toggle_play_pause)
        self.play_button.setEnabled(False)
        button_layout.addWidget(self.play_button)
        
        self.stop_button = QPushButton("Stop")
        self.stop_button.clicked.connect(self.stop)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)
        
        button_layout.addStretch()
        
        # Volume control
        volume_label = QLabel("Volume:")
        button_layout.addWidget(volume_label)
        
        self.volume_slider = QSlider(Qt.Orientation.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(70)
        self.volume_slider.setMaximumWidth(100)
        self.volume_slider.valueChanged.connect(self.set_volume)
        button_layout.addWidget(self.volume_slider)
        
        self.mute_button = QPushButton("Mute")
        self.mute_button.clicked.connect(self.toggle_mute)
        button_layout.addWidget(self.mute_button)
        
        button_layout.addStretch()
        
        self.fullscreen_button = QPushButton("Fullscreen")
        self.fullscreen_button.clicked.connect(self.toggle_fullscreen)
        button_layout.addWidget(self.fullscreen_button)
        
        controls_layout.addLayout(button_layout)
        
        return controls_layout
    
    def init_vlc(self):
        """Initialize VLC media player"""
        if not VLC_AVAILABLE:
            self.logger.warning("VLC not available, video playback disabled")
            return
        
        try:
            # Create VLC instance
            self.vlc_instance = vlc.Instance()
            self.media_player = self.vlc_instance.media_player_new()
            
            # Set video output to our widget
            if hasattr(self.video_frame, 'winId'):
                self.media_player.set_hwnd(int(self.video_frame.winId()))
            
            self.logger.info("VLC media player initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize VLC: {e}")
            self.vlc_instance = None
            self.media_player = None
    
    def play_channel(self, channel_data):
        """Play a channel"""
        if not self.media_player:
            self.logger.error("Media player not available")
            return
        
        self.current_channel = channel_data
        channel_url = channel_data.get('url', '')
        channel_name = channel_data.get('name', 'Unknown')
        
        self.logger.info(f"Playing channel: {channel_name}")
        
        try:
            # Create media
            media = self.vlc_instance.media_new(channel_url)
            self.media_player.set_media(media)
            
            # Start playback
            self.media_player.play()
            
            # Hide no video label
            self.no_video_label.hide()
            
            # Update UI
            self.play_button.setText("Pause")
            self.play_button.setEnabled(True)
            self.stop_button.setEnabled(True)
            
            # Start position timer
            self.position_timer.start(1000)  # Update every second
            
            self.playback_started.emit()
            
        except Exception as e:
            self.logger.error(f"Failed to play channel: {e}")
    
    def play(self):
        """Resume playback"""
        if self.media_player:
            self.media_player.play()
            self.play_button.setText("Pause")
            self.position_timer.start(1000)
            self.playback_started.emit()
    
    def pause(self):
        """Pause playback"""
        if self.media_player:
            self.media_player.pause()
            self.play_button.setText("Play")
            self.position_timer.stop()
            self.playback_paused.emit()
    
    def stop(self):
        """Stop playback"""
        if self.media_player:
            self.media_player.stop()
            
        # Update UI
        self.play_button.setText("Play")
        self.play_button.setEnabled(False)
        self.stop_button.setEnabled(False)
        
        # Show no video label
        self.no_video_label.show()
        
        # Stop timer
        self.position_timer.stop()
        
        # Reset progress
        self.progress_slider.setValue(0)
        self.time_label.setText("00:00")
        self.duration_label.setText("00:00")
        
        self.current_channel = None
        self.playback_stopped.emit()
    
    def toggle_play_pause(self):
        """Toggle between play and pause"""
        if not self.media_player:
            return
        
        if self.is_playing():
            self.pause()
        else:
            self.play()
    
    def is_playing(self):
        """Check if media is currently playing"""
        if self.media_player:
            state = self.media_player.get_state()
            return state == vlc.State.Playing
        return False
    
    def set_volume(self, volume):
        """Set playback volume"""
        if self.media_player:
            self.media_player.audio_set_volume(volume)
    
    def toggle_mute(self):
        """Toggle mute"""
        if self.media_player:
            is_muted = self.media_player.audio_get_mute()
            self.media_player.audio_set_mute(not is_muted)
            self.mute_button.setText("Unmute" if not is_muted else "Mute")
    
    def toggle_fullscreen(self):
        """Toggle fullscreen mode"""
        if self.media_player:
            self.is_fullscreen = not self.is_fullscreen
            self.media_player.set_fullscreen(self.is_fullscreen)
            self.fullscreen_button.setText("Exit Fullscreen" if self.is_fullscreen else "Fullscreen")
    
    def update_position(self):
        """Update playback position"""
        if not self.media_player:
            return
        
        try:
            # Get position (0.0 to 1.0)
            position = self.media_player.get_position()
            if position >= 0:
                self.progress_slider.setValue(int(position * 1000))
                self.position_changed.emit(position)
            
            # Get time
            current_time = self.media_player.get_time()
            if current_time >= 0:
                self.time_label.setText(self.format_time(current_time))
            
            # Get duration (for live streams, this might not be available)
            duration = self.media_player.get_length()
            if duration > 0:
                self.duration_label.setText(self.format_time(duration))
            else:
                self.duration_label.setText("LIVE")
                
        except Exception as e:
            self.logger.debug(f"Error updating position: {e}")
    
    def format_time(self, milliseconds):
        """Format time in milliseconds to MM:SS"""
        if milliseconds < 0:
            return "00:00"
        
        seconds = milliseconds // 1000
        minutes = seconds // 60
        seconds = seconds % 60
        
        return f"{minutes:02d}:{seconds:02d}"
    
    def on_slider_pressed(self):
        """Handle slider press"""
        self.position_timer.stop()
    
    def on_slider_released(self):
        """Handle slider release"""
        if self.media_player:
            position = self.progress_slider.value() / 1000.0
            self.media_player.set_position(position)
        
        if self.is_playing():
            self.position_timer.start(1000)
    
    def cleanup(self):
        """Cleanup resources"""
        self.position_timer.stop()
        
        if self.media_player:
            self.media_player.stop()
            self.media_player.release()
        
        if self.vlc_instance:
            self.vlc_instance.release()
        
        self.logger.info("Video player cleaned up")
