#!/usr/bin/env python3
"""
Test script to verify category filtering functionality
"""

import sys
import os

# Add src directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

def test_category_filtering():
    """Test category filtering logic"""
    
    print("🧪 Testing category filtering logic...")
    
    # Sample channels data
    channels = [
        {'name': 'BBC One', 'category': 'UK News', 'is_favorite': True},
        {'name': 'BBC Two', 'category': 'UK News', 'is_favorite': False},
        {'name': 'CNN', 'category': 'International News', 'is_favorite': False},
        {'name': 'Al Jazeera', 'category': 'International News', 'is_favorite': True},
        {'name': 'ESPN', 'category': 'Sports', 'is_favorite': False},
        {'name': 'Fox Sports', 'category': 'Sports', 'is_favorite': True},
        {'name': 'Discovery', 'category': 'Documentary', 'is_favorite': False},
        {'name': 'National Geographic', 'category': 'Documentary', 'is_favorite': True},
        {'name': 'MTV', 'category': 'Music', 'is_favorite': False},
        {'name': 'VH1', 'category': 'Music', 'is_favorite': False},
    ]
    
    print(f"📊 Total channels: {len(channels)}")
    
    # Test category extraction
    categories = set()
    for channel in channels:
        if channel.get('category'):
            categories.add(channel['category'])
    
    print(f"📂 Found categories: {sorted(categories)}")
    
    # Test preferred order
    preferred_order = [
        'News', 'International News', 'UK News', 'Arabic News',
        'Sports', 'Entertainment', 'Movies', 'Series',
        'Documentary', 'Kids', 'Music', 'Religious',
        'General', 'Other'
    ]
    
    sorted_categories = []
    
    # Add categories in preferred order if they exist
    for preferred_cat in preferred_order:
        if preferred_cat in categories:
            sorted_categories.append(preferred_cat)
            categories.remove(preferred_cat)
    
    # Add remaining categories alphabetically
    sorted_categories.extend(sorted(categories))
    
    print(f"🔄 Sorted categories: {sorted_categories}")
    
    # Test filtering for each category
    for category in sorted_categories:
        filtered = [ch for ch in channels if ch.get('category') == category]
        count = len(filtered)
        print(f"   📁 {category} ({count}): {[ch['name'] for ch in filtered]}")
    
    # Test channel sorting within category
    print("\n🔤 Testing channel sorting (favorites first, then alphabetical):")
    
    for category in sorted_categories:
        filtered = [ch for ch in channels if ch.get('category') == category]
        
        # Sort: favorites first, then alphabetically
        filtered.sort(key=lambda ch: (
            not ch.get('is_favorite', False),  # False comes before True
            ch.get('name', '').lower()
        ))
        
        print(f"   📁 {category}:")
        for ch in filtered:
            star = "⭐" if ch.get('is_favorite') else "  "
            print(f"      {star} {ch['name']}")
    
    print("\n✅ Category filtering test completed!")

if __name__ == '__main__':
    test_category_filtering()
