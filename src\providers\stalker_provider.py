"""
Stalker Portal provider for IPTV Player (MAG/STB Emulator)
"""

import json
import uuid
import time
import random
import asyncio
import aiohttp
from typing import List, Dict, Any, Optional
from datetime import datetime
from urllib.parse import urljoin, quote

from ..models.channel import Channel, ChannelGroup
from ..models.content import ContentInfo, ContentType, Series
from ..models.playlist import PlaylistCredentials, AuthType
from ..core.logger import LoggerMixin


class StalkerPortalProvider(LoggerMixin):
    """Provider for Stalker Portal (MAG/STB Emulator)"""
    
    def __init__(self, credentials: PlaylistCredentials):
        if credentials.auth_type != AuthType.MAC_ADDRESS:
            raise ValueError("Stalker Portal requires MAC address authentication")
        
        self.credentials = credentials
        self.portal_url = credentials.portal_url.rstrip('/')
        self.mac_address = credentials.mac_address
        self.session = None
        self.token = None
        self.device_info = self._generate_device_info()
        
        # Session management
        self.session_active = False
        self.last_activity = None
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers=self._get_default_headers()
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session_active:
            await self.logout()
        if self.session:
            await self.session.close()
    
    def _generate_device_info(self) -> Dict[str, Any]:
        """Generate MAG device information"""
        return {
            'sn': f"MAG{random.randint(100000, 999999)}",
            'device_id': str(uuid.uuid4()),
            'device_id2': str(uuid.uuid4()),
            'signature': str(uuid.uuid4()),
            'hw_version': '1.7-BD-00',
            'ver': 'ImageDescription: 0.2.18-r14-254; ImageDate: Fri Oct 23 16:38:54 EEST 2015',
            'image_version': '218',
            'hw_version_2': 'MAG254',
            'auth': '1',
            'login': 'admin',
            'password': '',
            'mac': self.mac_address,
            'stb_type': 'MAG254'
        }
    
    def _get_default_headers(self) -> Dict[str, str]:
        """Get default HTTP headers for MAG device simulation"""
        return {
            'User-Agent': 'Mozilla/5.0 (QtEmbedded; U; Linux; C) AppleWebKit/533.3 (KHTML, like Gecko) MAG200 stbapp ver: 2 rev: 250 Safari/533.3',
            'Accept': '*/*',
            'Accept-Language': 'en-US,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'X-User-Agent': 'Model: MAG254; Link: WiFi',
            'Referer': self.portal_url
        }
    
    async def authenticate(self) -> bool:
        """Authenticate with Stalker portal"""
        try:
            # Step 1: Handshake
            if not await self._handshake():
                return False
            
            # Step 2: Get profile
            if not await self._get_profile():
                return False
            
            # Step 3: Get token
            if not await self._get_token():
                return False
            
            self.session_active = True
            self.last_activity = datetime.now()
            self.logger.info("Stalker authentication successful")
            return True
            
        except Exception as e:
            self.logger.error(f"Authentication failed: {e}")
            return False
    
    async def _handshake(self) -> bool:
        """Perform initial handshake"""
        url = f"{self.portal_url}/server/load.php"
        params = {
            'type': 'stb',
            'action': 'handshake',
            'JsHttpRequest': f"1-xml",
            **self.device_info
        }
        
        try:
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('js', {}).get('result') == 'OK':
                        return True
                
                self.logger.error(f"Handshake failed: {response.status}")
                return False
                
        except Exception as e:
            self.logger.error(f"Handshake error: {e}")
            return False
    
    async def _get_profile(self) -> bool:
        """Get user profile"""
        url = f"{self.portal_url}/server/load.php"
        params = {
            'type': 'stb',
            'action': 'get_profile',
            'JsHttpRequest': f"1-xml",
            **self.device_info
        }
        
        try:
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'js' in data:
                        return True
                
                return False
                
        except Exception as e:
            self.logger.error(f"Get profile error: {e}")
            return False
    
    async def _get_token(self) -> bool:
        """Get authentication token"""
        url = f"{self.portal_url}/server/load.php"
        params = {
            'type': 'stb',
            'action': 'do_auth',
            'login': self.device_info['login'],
            'password': self.device_info['password'],
            'device_id': self.device_info['device_id'],
            'device_id2': self.device_info['device_id2'],
            'JsHttpRequest': f"1-xml",
            **self.device_info
        }
        
        try:
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    js_data = data.get('js', {})
                    
                    if js_data.get('token'):
                        self.token = js_data['token']
                        return True
                
                return False
                
        except Exception as e:
            self.logger.error(f"Get token error: {e}")
            return False
    
    async def get_live_channels(self) -> List[Channel]:
        """Get live TV channels"""
        if not await self._ensure_authenticated():
            return []
        
        # Get channel categories first
        categories = await self._get_itv_genres()
        channels = []
        
        for category in categories:
            category_channels = await self._get_itv_channels(category.get('id'))
            channels.extend(category_channels)
        
        return channels
    
    async def get_vod_content(self) -> List[ContentInfo]:
        """Get VOD content"""
        if not await self._ensure_authenticated():
            return []
        
        # Get VOD categories
        categories = await self._get_vod_genres()
        content_list = []
        
        for category in categories:
            category_content = await self._get_vod_by_genre(category.get('id'))
            content_list.extend(category_content)
        
        return content_list
    
    async def get_series(self) -> List[Series]:
        """Get series content"""
        if not await self._ensure_authenticated():
            return []
        
        # Get series categories
        categories = await self._get_series_genres()
        series_list = []
        
        for category in categories:
            category_series = await self._get_series_by_genre(category.get('id'))
            series_list.extend(category_series)
        
        return series_list
    
    async def _get_itv_genres(self) -> List[Dict[str, Any]]:
        """Get live TV categories"""
        return await self._api_request('itv', 'get_genres')
    
    async def _get_itv_channels(self, genre_id: Optional[str] = None) -> List[Channel]:
        """Get live TV channels for a category"""
        params = {'p': 1}
        if genre_id:
            params['genre'] = genre_id
        
        data = await self._api_request('itv', 'get_ordered_list', params)
        channels = []
        
        for item in data.get('data', []):
            try:
                channel = self._create_channel_from_itv(item)
                if channel:
                    channels.append(channel)
            except Exception as e:
                self.logger.error(f"Failed to create channel: {e}")
        
        return channels
    
    async def _get_vod_genres(self) -> List[Dict[str, Any]]:
        """Get VOD categories"""
        return await self._api_request('vod', 'get_categories')
    
    async def _get_vod_by_genre(self, genre_id: str) -> List[ContentInfo]:
        """Get VOD content for a category"""
        params = {'category_id': genre_id, 'p': 1}
        data = await self._api_request('vod', 'get_ordered_list', params)
        content_list = []
        
        for item in data.get('data', []):
            try:
                content = self._create_vod_from_item(item)
                if content:
                    content_list.append(content)
            except Exception as e:
                self.logger.error(f"Failed to create VOD content: {e}")
        
        return content_list
    
    async def _get_series_genres(self) -> List[Dict[str, Any]]:
        """Get series categories"""
        return await self._api_request('series', 'get_categories')
    
    async def _get_series_by_genre(self, genre_id: str) -> List[Series]:
        """Get series for a category"""
        params = {'category_id': genre_id, 'p': 1}
        data = await self._api_request('series', 'get_ordered_list', params)
        series_list = []
        
        for item in data.get('data', []):
            try:
                series = await self._create_series_from_item(item)
                if series:
                    series_list.append(series)
            except Exception as e:
                self.logger.error(f"Failed to create series: {e}")
        
        return series_list
    
    async def get_stream_url(self, cmd: str, stream_type: str = 'live') -> str:
        """Get stream URL for content"""
        if not await self._ensure_authenticated():
            return ""
        
        if stream_type == 'live':
            params = {'cmd': cmd}
            data = await self._api_request('itv', 'create_link', params)
            return data.get('cmd', '')
        elif stream_type == 'vod':
            params = {'cmd': cmd}
            data = await self._api_request('vod', 'create_link', params)
            return data.get('cmd', '')
        
        return ""
    
    async def _api_request(self, module: str, action: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make API request to Stalker portal"""
        if not await self._ensure_authenticated():
            return {}
        
        url = f"{self.portal_url}/server/load.php"
        request_params = {
            'type': module,
            'action': action,
            'JsHttpRequest': f"{int(time.time())}-xml",
            **self.device_info
        }
        
        if self.token:
            request_params['token'] = self.token
        
        if params:
            request_params.update(params)
        
        try:
            async with self.session.get(url, params=request_params) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get('js', {})
                else:
                    self.logger.error(f"API request failed: {response.status}")
                    return {}
                    
        except Exception as e:
            self.logger.error(f"API request error: {e}")
            return {}
    
    def _create_channel_from_itv(self, item: Dict[str, Any]) -> Optional[Channel]:
        """Create Channel from ITV item"""
        try:
            channel_id = str(item.get('id', ''))
            if not channel_id:
                return None
            
            channel = Channel(
                id=f"stalker_live_{channel_id}",
                name=item.get('name', 'Unknown Channel'),
                url="",  # Will be resolved when playing
                group=item.get('tv_genre_name'),
                logo=item.get('logo'),
                category=item.get('tv_genre_name')
            )
            
            # Add Stalker-specific metadata
            channel.metadata.update({
                'cmd': item.get('cmd'),
                'tv_genre_id': item.get('tv_genre_id'),
                'number': item.get('number'),
                'use_http_tmp_link': item.get('use_http_tmp_link'),
                'use_load_balancing': item.get('use_load_balancing')
            })
            
            return channel
            
        except Exception as e:
            self.logger.error(f"Failed to create channel: {e}")
            return None
    
    def _create_vod_from_item(self, item: Dict[str, Any]) -> Optional[ContentInfo]:
        """Create ContentInfo from VOD item"""
        try:
            item_id = str(item.get('id', ''))
            if not item_id:
                return None
            
            content = ContentInfo(
                id=f"stalker_vod_{item_id}",
                title=item.get('name', 'Unknown Movie'),
                content_type=ContentType.VOD,
                url="",  # Will be resolved when playing
                description=item.get('description'),
                poster=item.get('screenshot_uri'),
                genre=item.get('category_name'),
                year=self._parse_year(item.get('year')),
                duration=self._parse_duration(item.get('time'))
            )
            
            # Add Stalker-specific metadata
            content.metadata.update({
                'cmd': item.get('cmd'),
                'category_id': item.get('category_id'),
                'rating_imdb': item.get('rating_imdb'),
                'rating_kinopoisk': item.get('rating_kinopoisk'),
                'country': item.get('country'),
                'director': item.get('director'),
                'actors': item.get('actors')
            })
            
            return content
            
        except Exception as e:
            self.logger.error(f"Failed to create VOD content: {e}")
            return None
    
    async def _create_series_from_item(self, item: Dict[str, Any]) -> Optional[Series]:
        """Create Series from series item"""
        try:
            item_id = str(item.get('id', ''))
            if not item_id:
                return None
            
            series = Series(
                id=f"stalker_series_{item_id}",
                title=item.get('name', 'Unknown Series'),
                content_type=ContentType.SERIES,
                url="",  # Series don't have direct URLs
                description=item.get('description'),
                poster=item.get('screenshot_uri'),
                genre=item.get('category_name'),
                year=self._parse_year(item.get('year'))
            )
            
            # Add Stalker-specific metadata
            series.metadata.update({
                'series_id': item_id,
                'category_id': item.get('category_id'),
                'rating_imdb': item.get('rating_imdb'),
                'rating_kinopoisk': item.get('rating_kinopoisk'),
                'country': item.get('country'),
                'director': item.get('director'),
                'actors': item.get('actors')
            })
            
            return series
            
        except Exception as e:
            self.logger.error(f"Failed to create series: {e}")
            return None
    
    async def _ensure_authenticated(self) -> bool:
        """Ensure we have valid authentication"""
        if not self.session_active or not self.token:
            return await self.authenticate()
        
        # Check if we need to refresh the session
        if self.last_activity:
            time_since_activity = datetime.now() - self.last_activity
            if time_since_activity.total_seconds() > 1800:  # 30 minutes
                return await self.authenticate()
        
        self.last_activity = datetime.now()
        return True
    
    async def logout(self) -> None:
        """Logout from Stalker portal"""
        if self.session_active:
            try:
                await self._api_request('stb', 'logout')
            except Exception as e:
                self.logger.error(f"Logout error: {e}")
            finally:
                self.session_active = False
                self.token = None
    
    def _parse_year(self, year_str: Any) -> Optional[int]:
        """Parse year from string"""
        if not year_str:
            return None
        try:
            return int(str(year_str))
        except (ValueError, TypeError):
            return None
    
    def _parse_duration(self, duration_str: Any) -> Optional[int]:
        """Parse duration to seconds"""
        if not duration_str:
            return None
        try:
            # Handle "HH:MM:SS" format
            if isinstance(duration_str, str) and ':' in duration_str:
                parts = duration_str.split(':')
                if len(parts) == 3:
                    hours, minutes, seconds = map(int, parts)
                    return hours * 3600 + minutes * 60 + seconds
            return int(duration_str) * 60  # Assume minutes if just a number
        except (ValueError, TypeError):
            return None
