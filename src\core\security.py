"""
Security and authentication manager for IPTV Player
"""

import os
import hashlib
import secrets
import time
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta

try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    import keyring
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False

from .logger import LoggerMixin
from .app_config import get_config


class SecurityManager(LoggerMixin):
    """Manages security features including PIN authentication and credential encryption"""
    
    def __init__(self):
        self.config = get_config()
        self.session_start_time = None
        self.last_activity_time = None
        self.is_authenticated = False
        self.failed_attempts = 0
        self.lockout_until = None
        
        # Initialize encryption if available
        self.cipher_suite = None
        if CRYPTO_AVAILABLE:
            self._initialize_encryption()
    
    def _initialize_encryption(self):
        """Initialize encryption for credential storage"""
        try:
            # Try to get existing key from keyring
            key = keyring.get_password("iptv_player", "encryption_key")
            
            if not key:
                # Generate new key
                key = Fernet.generate_key().decode()
                keyring.set_password("iptv_player", "encryption_key", key)
                self.logger.info("Generated new encryption key")
            
            self.cipher_suite = Fernet(key.encode())
            
        except Exception as e:
            self.logger.warning(f"Failed to initialize encryption: {e}")
            self.cipher_suite = None
    
    def hash_pin(self, pin: str) -> str:
        """Hash PIN for secure storage"""
        # Use SHA-256 with salt
        salt = b'iptv_player_salt'  # In production, use random salt per user
        return hashlib.pbkdf2_hmac('sha256', pin.encode(), salt, 100000).hex()
    
    def verify_pin(self, pin: str, stored_hash: str) -> bool:
        """Verify PIN against stored hash"""
        return self.hash_pin(pin) == stored_hash
    
    def authenticate_pin(self, pin: str, stored_pin_hash: str) -> bool:
        """Authenticate user with PIN"""
        # Check if locked out
        if self.is_locked_out():
            self.logger.warning("Authentication blocked due to lockout")
            return False
        
        # Verify PIN
        if self.verify_pin(pin, stored_pin_hash):
            self.is_authenticated = True
            self.session_start_time = datetime.now()
            self.last_activity_time = datetime.now()
            self.failed_attempts = 0
            self.lockout_until = None
            self.logger.info("PIN authentication successful")
            return True
        else:
            self.failed_attempts += 1
            self.logger.warning(f"PIN authentication failed (attempt {self.failed_attempts})")
            
            # Lock out after 3 failed attempts
            if self.failed_attempts >= 3:
                self.lockout_until = datetime.now() + timedelta(minutes=5)
                self.logger.warning("Account locked due to failed attempts")
            
            return False
    
    def is_locked_out(self) -> bool:
        """Check if account is locked out"""
        if self.lockout_until and datetime.now() < self.lockout_until:
            return True
        
        # Clear lockout if time has passed
        if self.lockout_until and datetime.now() >= self.lockout_until:
            self.lockout_until = None
            self.failed_attempts = 0
        
        return False
    
    def get_lockout_remaining(self) -> Optional[int]:
        """Get remaining lockout time in seconds"""
        if self.is_locked_out():
            remaining = (self.lockout_until - datetime.now()).total_seconds()
            return max(0, int(remaining))
        return None
    
    def update_activity(self):
        """Update last activity time"""
        self.last_activity_time = datetime.now()
    
    def check_session_timeout(self, timeout_minutes: int) -> bool:
        """Check if session has timed out"""
        if not self.is_authenticated or not self.last_activity_time:
            return True
        
        if timeout_minutes <= 0:
            return False  # No timeout
        
        timeout_time = self.last_activity_time + timedelta(minutes=timeout_minutes)
        if datetime.now() > timeout_time:
            self.logout()
            return True
        
        return False
    
    def logout(self):
        """Logout and clear session"""
        self.is_authenticated = False
        self.session_start_time = None
        self.last_activity_time = None
        self.logger.info("User logged out")
    
    def encrypt_credentials(self, data: str) -> Optional[str]:
        """Encrypt sensitive data"""
        if not self.cipher_suite:
            self.logger.warning("Encryption not available, storing data in plain text")
            return data
        
        try:
            encrypted_data = self.cipher_suite.encrypt(data.encode())
            return encrypted_data.decode()
        except Exception as e:
            self.logger.error(f"Failed to encrypt data: {e}")
            return None
    
    def decrypt_credentials(self, encrypted_data: str) -> Optional[str]:
        """Decrypt sensitive data"""
        if not self.cipher_suite:
            # Data might be stored in plain text
            return encrypted_data
        
        try:
            decrypted_data = self.cipher_suite.decrypt(encrypted_data.encode())
            return decrypted_data.decode()
        except Exception as e:
            self.logger.error(f"Failed to decrypt data: {e}")
            return None
    
    def is_content_allowed(self, content_rating: str, user_rating_limit: str) -> bool:
        """Check if content is allowed based on rating"""
        if not user_rating_limit:
            return True
        
        # Rating hierarchy (lower number = more restrictive)
        rating_levels = {
            'G': 1,
            'PG': 2,
            'PG-13': 3,
            'R': 4,
            'NC-17': 5,
            'TV-Y': 1,
            'TV-Y7': 2,
            'TV-G': 2,
            'TV-PG': 3,
            'TV-14': 4,
            'TV-MA': 5,
            'Unrated': 5
        }
        
        content_level = rating_levels.get(content_rating, 5)
        limit_level = rating_levels.get(user_rating_limit, 5)
        
        return content_level <= limit_level
    
    def is_category_locked(self, category: str, locked_categories: list) -> bool:
        """Check if category is locked"""
        return category.lower() in [cat.lower() for cat in locked_categories]
    
    def generate_secure_token(self, length: int = 32) -> str:
        """Generate secure random token"""
        return secrets.token_urlsafe(length)
    
    def validate_password_strength(self, password: str) -> Dict[str, Any]:
        """Validate password strength"""
        result = {
            'is_strong': True,
            'score': 0,
            'issues': []
        }
        
        # Length check
        if len(password) < 8:
            result['issues'].append("Password must be at least 8 characters long")
            result['is_strong'] = False
        else:
            result['score'] += 1
        
        # Character variety checks
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
        
        if has_upper:
            result['score'] += 1
        else:
            result['issues'].append("Password should contain uppercase letters")
        
        if has_lower:
            result['score'] += 1
        else:
            result['issues'].append("Password should contain lowercase letters")
        
        if has_digit:
            result['score'] += 1
        else:
            result['issues'].append("Password should contain numbers")
        
        if has_special:
            result['score'] += 1
        else:
            result['issues'].append("Password should contain special characters")
        
        # Overall strength
        if result['score'] < 3:
            result['is_strong'] = False
        
        return result
    
    def secure_delete_file(self, file_path: str):
        """Securely delete a file by overwriting it"""
        if not os.path.exists(file_path):
            return
        
        try:
            # Get file size
            file_size = os.path.getsize(file_path)
            
            # Overwrite with random data multiple times
            with open(file_path, 'r+b') as file:
                for _ in range(3):
                    file.seek(0)
                    file.write(os.urandom(file_size))
                    file.flush()
                    os.fsync(file.fileno())
            
            # Finally delete the file
            os.remove(file_path)
            self.logger.info(f"Securely deleted file: {file_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to securely delete file {file_path}: {e}")
    
    def get_security_status(self) -> Dict[str, Any]:
        """Get current security status"""
        return {
            'is_authenticated': self.is_authenticated,
            'is_locked_out': self.is_locked_out(),
            'lockout_remaining': self.get_lockout_remaining(),
            'failed_attempts': self.failed_attempts,
            'session_duration': (
                (datetime.now() - self.session_start_time).total_seconds()
                if self.session_start_time else 0
            ),
            'encryption_available': CRYPTO_AVAILABLE and self.cipher_suite is not None
        }


class ParentalControlManager(LoggerMixin):
    """Manages parental control features"""
    
    def __init__(self, security_manager: SecurityManager):
        self.security_manager = security_manager
        self.blocked_content = set()
        self.viewing_history = []
    
    def is_content_blocked(self, content_id: str, content_rating: str, 
                          category: str, user_settings: Dict[str, Any]) -> bool:
        """Check if content should be blocked"""
        # Check if parental controls are enabled
        if not user_settings.get('parental_control_enabled', False):
            return False
        
        # Check content rating
        rating_limit = user_settings.get('content_rating_limit')
        if rating_limit and not self.security_manager.is_content_allowed(content_rating, rating_limit):
            self.logger.info(f"Content blocked due to rating: {content_rating}")
            return True
        
        # Check locked categories
        locked_categories = user_settings.get('locked_categories', [])
        if self.security_manager.is_category_locked(category, locked_categories):
            self.logger.info(f"Content blocked due to category: {category}")
            return True
        
        # Check manually blocked content
        if content_id in self.blocked_content:
            self.logger.info(f"Content manually blocked: {content_id}")
            return True
        
        return False
    
    def block_content(self, content_id: str):
        """Manually block specific content"""
        self.blocked_content.add(content_id)
        self.logger.info(f"Content blocked: {content_id}")
    
    def unblock_content(self, content_id: str):
        """Unblock specific content"""
        self.blocked_content.discard(content_id)
        self.logger.info(f"Content unblocked: {content_id}")
    
    def log_viewing_activity(self, content_id: str, content_title: str, 
                           duration_seconds: int):
        """Log viewing activity for monitoring"""
        activity = {
            'content_id': content_id,
            'title': content_title,
            'timestamp': datetime.now(),
            'duration': duration_seconds
        }
        
        self.viewing_history.append(activity)
        
        # Keep only last 1000 entries
        if len(self.viewing_history) > 1000:
            self.viewing_history = self.viewing_history[-1000:]
        
        self.logger.debug(f"Logged viewing activity: {content_title}")
    
    def get_viewing_report(self, days: int = 7) -> List[Dict[str, Any]]:
        """Get viewing activity report"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        recent_activity = [
            activity for activity in self.viewing_history
            if activity['timestamp'] >= cutoff_date
        ]
        
        return recent_activity
    
    def get_content_statistics(self) -> Dict[str, Any]:
        """Get content viewing statistics"""
        if not self.viewing_history:
            return {'total_content': 0, 'total_time': 0, 'categories': {}}
        
        total_time = sum(activity['duration'] for activity in self.viewing_history)
        total_content = len(set(activity['content_id'] for activity in self.viewing_history))
        
        # Category breakdown would require additional content metadata
        categories = {}
        
        return {
            'total_content': total_content,
            'total_time': total_time,
            'categories': categories,
            'average_session': total_time / len(self.viewing_history) if self.viewing_history else 0
        }
