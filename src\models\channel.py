"""
Channel data model for IPTV Player
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from datetime import datetime
import json


@dataclass
class Channel:
    """Represents a TV channel"""
    
    id: str
    name: str
    url: str
    group: Optional[str] = None
    logo: Optional[str] = None
    epg_id: Optional[str] = None
    country: Optional[str] = None
    language: Optional[str] = None
    category: Optional[str] = None
    is_favorite: bool = False
    is_hidden: bool = False
    is_locked: bool = False
    sort_order: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert channel to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'url': self.url,
            'group': self.group,
            'logo': self.logo,
            'epg_id': self.epg_id,
            'country': self.country,
            'language': self.language,
            'category': self.category,
            'is_favorite': self.is_favorite,
            'is_hidden': self.is_hidden,
            'is_locked': self.is_locked,
            'sort_order': self.sort_order,
            'metadata': json.dumps(self.metadata),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Channel':
        """Create channel from dictionary"""
        metadata = data.get('metadata', '{}')
        if isinstance(metadata, str):
            metadata = json.loads(metadata)
        
        return cls(
            id=data['id'],
            name=data['name'],
            url=data['url'],
            group=data.get('group'),
            logo=data.get('logo'),
            epg_id=data.get('epg_id'),
            country=data.get('country'),
            language=data.get('language'),
            category=data.get('category'),
            is_favorite=data.get('is_favorite', False),
            is_hidden=data.get('is_hidden', False),
            is_locked=data.get('is_locked', False),
            sort_order=data.get('sort_order', 0),
            metadata=metadata,
            created_at=datetime.fromisoformat(data.get('created_at', datetime.now().isoformat())),
            updated_at=datetime.fromisoformat(data.get('updated_at', datetime.now().isoformat()))
        )
    
    def update_metadata(self, key: str, value: Any) -> None:
        """Update metadata field"""
        self.metadata[key] = value
        self.updated_at = datetime.now()
    
    def get_display_name(self) -> str:
        """Get display name for the channel"""
        return self.name or f"Channel {self.id}"
    
    def get_category_display(self) -> str:
        """Get category for display"""
        return self.category or self.group or "Uncategorized"


@dataclass
class ChannelGroup:
    """Represents a group/category of channels"""
    
    name: str
    channels: List[Channel] = field(default_factory=list)
    is_hidden: bool = False
    sort_order: int = 0
    
    def add_channel(self, channel: Channel) -> None:
        """Add channel to group"""
        if channel not in self.channels:
            self.channels.append(channel)
    
    def remove_channel(self, channel: Channel) -> None:
        """Remove channel from group"""
        if channel in self.channels:
            self.channels.remove(channel)
    
    def get_visible_channels(self) -> List[Channel]:
        """Get non-hidden channels"""
        return [ch for ch in self.channels if not ch.is_hidden]
    
    def get_favorite_channels(self) -> List[Channel]:
        """Get favorite channels"""
        return [ch for ch in self.channels if ch.is_favorite]
