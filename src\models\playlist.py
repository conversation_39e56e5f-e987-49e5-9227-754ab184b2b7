"""
Playlist data models for IPTV Player
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum
import json


class PlaylistType(Enum):
    """Types of playlists supported"""
    M3U = "m3u"
    M3U8 = "m3u8"
    XTREAM = "xtream"
    STALKER = "stalker"


class AuthType(Enum):
    """Authentication types"""
    NONE = "none"
    USERNAME_PASSWORD = "username_password"
    MAC_ADDRESS = "mac_address"
    TOKEN = "token"


@dataclass
class PlaylistCredentials:
    """Authentication credentials for playlists"""
    
    auth_type: AuthType
    username: Optional[str] = None
    password: Optional[str] = None
    mac_address: Optional[str] = None
    token: Optional[str] = None
    server_url: Optional[str] = None
    portal_url: Optional[str] = None
    additional_params: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert credentials to dictionary"""
        return {
            'auth_type': self.auth_type.value,
            'username': self.username,
            'password': self.password,
            'mac_address': self.mac_address,
            'token': self.token,
            'server_url': self.server_url,
            'portal_url': self.portal_url,
            'additional_params': json.dumps(self.additional_params)
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PlaylistCredentials':
        """Create credentials from dictionary"""
        additional_params = data.get('additional_params', '{}')
        if isinstance(additional_params, str):
            additional_params = json.loads(additional_params)
        
        return cls(
            auth_type=AuthType(data['auth_type']),
            username=data.get('username'),
            password=data.get('password'),
            mac_address=data.get('mac_address'),
            token=data.get('token'),
            server_url=data.get('server_url'),
            portal_url=data.get('portal_url'),
            additional_params=additional_params
        )


@dataclass
class Playlist:
    """Represents an IPTV playlist"""
    
    id: str
    name: str
    playlist_type: PlaylistType
    url: Optional[str] = None
    file_path: Optional[str] = None
    credentials: Optional[PlaylistCredentials] = None
    is_active: bool = True
    auto_refresh: bool = False
    refresh_interval: int = 3600  # seconds
    last_updated: Optional[datetime] = None
    channel_count: int = 0
    vod_count: int = 0
    series_count: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert playlist to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'playlist_type': self.playlist_type.value,
            'url': self.url,
            'file_path': self.file_path,
            'credentials': self.credentials.to_dict() if self.credentials else None,
            'is_active': self.is_active,
            'auto_refresh': self.auto_refresh,
            'refresh_interval': self.refresh_interval,
            'last_updated': self.last_updated.isoformat() if self.last_updated else None,
            'channel_count': self.channel_count,
            'vod_count': self.vod_count,
            'series_count': self.series_count,
            'metadata': json.dumps(self.metadata),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Playlist':
        """Create playlist from dictionary"""
        metadata = data.get('metadata', '{}')
        if isinstance(metadata, str):
            metadata = json.loads(metadata)
        
        credentials = None
        if data.get('credentials'):
            credentials = PlaylistCredentials.from_dict(data['credentials'])
        
        return cls(
            id=data['id'],
            name=data['name'],
            playlist_type=PlaylistType(data['playlist_type']),
            url=data.get('url'),
            file_path=data.get('file_path'),
            credentials=credentials,
            is_active=data.get('is_active', True),
            auto_refresh=data.get('auto_refresh', False),
            refresh_interval=data.get('refresh_interval', 3600),
            last_updated=datetime.fromisoformat(data['last_updated']) if data.get('last_updated') else None,
            channel_count=data.get('channel_count', 0),
            vod_count=data.get('vod_count', 0),
            series_count=data.get('series_count', 0),
            metadata=metadata,
            created_at=datetime.fromisoformat(data.get('created_at', datetime.now().isoformat())),
            updated_at=datetime.fromisoformat(data.get('updated_at', datetime.now().isoformat()))
        )
    
    def needs_refresh(self) -> bool:
        """Check if playlist needs refresh"""
        if not self.auto_refresh or not self.last_updated:
            return False
        
        time_diff = datetime.now() - self.last_updated
        return time_diff.total_seconds() >= self.refresh_interval
    
    def mark_updated(self) -> None:
        """Mark playlist as updated"""
        self.last_updated = datetime.now()
        self.updated_at = datetime.now()
