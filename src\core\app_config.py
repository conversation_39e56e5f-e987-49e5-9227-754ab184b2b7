"""
Application configuration manager
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class AppConfig:
    """Application configuration"""
    
    # Application info
    app_name: str = "IPTV Player"
    app_version: str = "1.0.0"
    
    # Directories
    data_dir: str = ""
    cache_dir: str = ""
    logs_dir: str = ""
    playlists_dir: str = ""
    
    # Database
    database_path: str = ""
    
    # Network settings
    request_timeout: int = 30
    max_retries: int = 3
    user_agent: str = "IPTV Player/1.0.0"
    
    # Cache settings
    cache_enabled: bool = True
    cache_size_mb: int = 500
    cache_ttl_hours: int = 24
    
    # Player settings
    default_player_backend: str = "vlc"
    buffer_size_ms: int = 5000
    network_timeout_ms: int = 30000
    
    # UI settings
    window_width: int = 1280
    window_height: int = 720
    min_window_width: int = 800
    min_window_height: int = 600
    
    # Security
    enable_https_verification: bool = True
    allow_insecure_connections: bool = False
    
    # Logging
    log_level: str = "INFO"
    log_max_files: int = 5
    log_max_size_mb: int = 10
    
    def __post_init__(self):
        """Initialize configuration after creation"""
        if not self.data_dir:
            self.data_dir = str(Path.home() / '.iptv_player')
        
        self.cache_dir = os.path.join(self.data_dir, 'cache')
        self.logs_dir = os.path.join(self.data_dir, 'logs')
        self.playlists_dir = os.path.join(self.data_dir, 'playlists')
        self.database_path = os.path.join(self.data_dir, 'iptv_player.db')
        
        # Create directories
        for directory in [self.data_dir, self.cache_dir, self.logs_dir, self.playlists_dir]:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AppConfig':
        """Create config from dictionary"""
        return cls(**data)
    
    def save_to_file(self, config_path: Optional[str] = None) -> None:
        """Save configuration to file"""
        if config_path is None:
            config_path = os.path.join(self.data_dir, 'config.json')
        
        try:
            with open(config_path, 'w') as f:
                json.dump(self.to_dict(), f, indent=2)
        except Exception as e:
            logging.error(f"Failed to save config: {e}")
    
    @classmethod
    def load_from_file(cls, config_path: Optional[str] = None) -> 'AppConfig':
        """Load configuration from file"""
        if config_path is None:
            data_dir = Path.home() / '.iptv_player'
            config_path = str(data_dir / 'config.json')
        
        try:
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    data = json.load(f)
                return cls.from_dict(data)
        except Exception as e:
            logging.warning(f"Failed to load config, using defaults: {e}")
        
        # Return default config if loading fails
        config = cls()
        config.save_to_file(config_path)
        return config
    
    def get_user_agent(self) -> str:
        """Get user agent string"""
        return f"{self.app_name}/{self.app_version}"
    
    def get_request_headers(self) -> Dict[str, str]:
        """Get default request headers"""
        return {
            'User-Agent': self.get_user_agent(),
            'Accept': '*/*',
            'Connection': 'keep-alive'
        }
    
    def get_cache_path(self, key: str) -> str:
        """Get cache file path for a key"""
        return os.path.join(self.cache_dir, f"{key}.cache")
    
    def get_playlist_path(self, playlist_id: str) -> str:
        """Get playlist file path"""
        return os.path.join(self.playlists_dir, f"{playlist_id}.m3u")
    
    def is_debug_mode(self) -> bool:
        """Check if debug mode is enabled"""
        return self.log_level.upper() == "DEBUG"
    
    def validate(self) -> bool:
        """Validate configuration"""
        try:
            # Check required directories exist
            for directory in [self.data_dir, self.cache_dir, self.logs_dir, self.playlists_dir]:
                if not os.path.exists(directory):
                    Path(directory).mkdir(parents=True, exist_ok=True)
            
            # Validate numeric values
            if self.request_timeout <= 0:
                self.request_timeout = 30
            
            if self.cache_size_mb <= 0:
                self.cache_size_mb = 500
            
            if self.window_width < self.min_window_width:
                self.window_width = self.min_window_width
            
            if self.window_height < self.min_window_height:
                self.window_height = self.min_window_height
            
            return True
            
        except Exception as e:
            logging.error(f"Config validation failed: {e}")
            return False


# Global configuration instance
_config_instance = None


def get_config() -> AppConfig:
    """Get global configuration instance"""
    global _config_instance
    if _config_instance is None:
        _config_instance = AppConfig.load_from_file()
    return _config_instance


def reload_config() -> AppConfig:
    """Reload configuration from file"""
    global _config_instance
    _config_instance = AppConfig.load_from_file()
    return _config_instance
